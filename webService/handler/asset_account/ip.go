// IP资产

package asset_account

import (
	"context"
	"encoding/json"
	"fmt"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/mysql"
	cluesModel "micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/ip_history"
	"micro-service/middleware/mysql/user"
	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/clues"
	"micro-service/pkg/log"
	"micro-service/pkg/storage"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	elastic_search "github.com/olivere/elastic"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"

	"micro-service/coreService/handler/vuln"
	"micro-service/initialize/es"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/fofaee_service"
	"micro-service/middleware/elastic/fofaee_subdomain"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/utils"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
)

// IPProfile ip画像
func IPProfile(userId int, ip string) (*pb.IpAssetProfileResponse, error) {
	// 获取fofaee_assets数据
	client := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance())
	list, err := client.FindByIpAndPort(userId, []elastic.IpPort{{Ip: ip}})
	if err != nil || len(list) == 0 {
		return nil, err
	}

	// 根据IP获取fofaee_subdomain数据
	domainList, err := fofaee_subdomain.NewFofeeSubdomainModel().FindByIpPort(elastic.IpPort{Ip: ip})
	if err != nil {
		return nil, err
	}
	domainMap := convertSubdomain(domainList)

	// 根据IP获取fofaee_service数据
	serviceList, err := fofaee_service.NewFofeeServiceModel().FindByIpPort(elastic.IpPort{Ip: ip})
	if err != nil {
		return nil, err
	}
	serviceMap := convertService(serviceList)

	info := new(pb.IpAssetProfileResponse)
	item := list[0]
	info.Ip = item.Ip
	for i := range item.PortList {
		port := cast.ToString(item.PortList[i].Port)
		unit := &pb.IpAssetProfileResponse_Unit{
			Port:      port,
			Protocol:  item.PortList[i].Protocol,
			PortLayer: componentsByLevel(domainMap[port], serviceMap[port]),
		}
		info.Items = append(info.Items, unit)
	}

	return info, nil
}

func IpAssetVulns(ctx context.Context, req *pb.IpAssetIpVulnsRequest, rsp *pb.IpAssetIpVulnsResponse) error {
	// 获取fofaee_assets数据
	client := fofaee_assets.NewFofaeeAssetsModel(es.GetInstance())
	l, err := client.FindByIpAndPort(int(req.UserId), []elastic.IpPort{{Ip: req.Ip}})
	if err != nil || len(l) == 0 {
		return err
	}

	ports := l[0].PortList
	var list = make([]*vuln.AssetMetadata, 0, len(l))
	for i := range ports {
		if ports[i].Banner == "" && ports[i].Header == "" {
			continue
		}
		banner := utils.If(ports[i].Banner != "", ports[i].Banner, ports[i].Header)
		list = append(list, &vuln.AssetMetadata{
			Ip:       req.Ip,
			Port:     cast.ToString(ports[i].Port),
			Banner:   banner,
			Protocol: ports[i].Protocol,
		})
	}

	result, err := vuln.GetVulnsWithHB(ctx, list)
	if err != nil {
		return err
	}

	for i := range result {
		rsp.Items = append(rsp.Items, &pb.IpAssetIpVulnsResponse_VulnInfo{
			Ip:            result[i].Ip,
			Port:          result[i].Port,
			CveId:         result[i].CVEId,
			Name:          "", // 漏洞名称，暂无数据
			CvssVersion:   result[i].CvssVersion,
			Level:         result[i].Level,
			Score:         result[i].Score,
			Description:   result[i].Description,
			PublishedDate: cutPublishedDate(result[i].PublishedDate), // YYYY-MM-DD
			FindDate:      time.Now().Format(utils.DateLayout),       // 取当前日期
		})
	}

	return nil
}

func AssetsAccountIpList(_ context.Context, req *pb.IpAssetListRequest, rsp *pb.IpAssetListResponse) error {
	log.Infof("[IP资产] 列表查询请求: %v", req)

	// 详细打印请求参数
	reqJSON, _ := json.Marshal(req)
	log.Infof("【详细调试】完整请求参数: %s", string(reqJSON))

	// 特别打印时间相关参数
	log.Infof("【时间调试】CreatedAt: %v", req.CreatedAt)
	log.Infof("【时间调试】UpdatedAt: %v", req.UpdatedAt)

	var sorts []elastic_search.Sorter
	if req.Sort != nil {
		sorts = append(sorts, elastic_search.NewFieldSort("updated_at").Desc())
	} else {
		sorts = append(sorts, elastic_search.NewFieldSort("ip").Asc())
	}
	var builder elastic.SearchBuilder
	elastic.StructToParams(req, &builder)

	// 打印StructToParams后的查询条件
	log.Infof("【ES查询调试】StructToParams后的查询条件: %v", builder.Build())
	// 关键字
	if req.Keyword != nil {
		var keywordQuery [][]interface{}
		_, err := strconv.Atoi(*req.Keyword)
		if err == nil {
			keywordQuery = append(keywordQuery, []interface{}{"port_list.logo.hash", *req.Keyword})
			keywordQuery = append(keywordQuery, []interface{}{"port", *req.Keyword})
		}
		keywordQuery = append(keywordQuery, []interface{}{"port_list.cert.subject_key", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.icp.no", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.title.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url.raw", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.protocol", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.url", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"host_list.url.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"custom_tags.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"ip.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		keywordQuery = append(keywordQuery, []interface{}{"clue_company_name.keyword", "like", elastic.WithRLAsterisk(*req.Keyword)})
		builder.AddShould(keywordQuery...)
	}
	// Title
	if len(req.Title) > 0 {
		if utils.ListContains(req.Title, "-") {
			names := utils.ListReplace(req.Title, "-", "")
			if len(names) == 1 {
				// all_title不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
				// 方法2: 字段为null
				builder.AddShould([]interface{}{"all_title", "=", nil})
			} else {
				builder.AddShould([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(names)})
				// all_title不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_title", "EXISTS"}}})
				// 方法2: 字段为null
				builder.AddShould([]interface{}{"all_title", "=", nil})
			}
		} else {
			builder.AddMust([]interface{}{"host_list.title.keyword", "in", elastic.ToInterfaceArray(req.Title)})
		}
	}
	// Domain
	if len(req.Domain) > 0 {
		if utils.ListContains(req.Domain, "-") {
			names := utils.ListReplace(req.Domain, "-", "")
			if len(names) == 1 {
				// all_domain不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
				// 方法2: 字段为null (在ES中null值通常不被索引，但为了完整性添加)
				builder.AddShould([]interface{}{"all_domain", "=", nil})
			} else {
				builder.AddShould([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(names)})
				// all_domain不存在或为null的情况
				// 方法1: 字段不存在
				builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"all_domain", "EXISTS"}}})
				// 方法2: 字段为null
				builder.AddShould([]interface{}{"all_domain", "=", nil})
			}
		} else {
			builder.AddMust([]interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(req.Domain)})
		}
	}
	// Hosts
	if req.Hosts != nil {
		var hostsQuery [][]interface{}
		hostsQuery = append(hostsQuery, []interface{}{"port_list.domain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"port_list.subdomain", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.domain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
		hostsQuery = append(hostsQuery, []interface{}{"host_list.subdomain.keyword", "like", elastic.WithRLAsterisk(*req.Hosts)})
		builder.AddShould(hostsQuery...)
	}
	// Assets Source
	if len(req.AssetsSource) > 0 {
		if utils.ListContains(req.AssetsSource, "-") {
			names := utils.ListReplace(req.AssetsSource, "-", "")
			// 过滤掉空字符串，避免ES查询错误
			filteredNames := make([]string, 0)
			for _, name := range names {
				if name != "" {
					filteredNames = append(filteredNames, name)
				}
			}

			// 只有在有有效的资产来源时才添加查询条件
			if len(filteredNames) > 0 {
				builder.AddShould([]interface{}{"host_list.assets_source", "in", elastic.ToInterfaceArray(filteredNames)})
			}
			// 添加不存在assets_source字段的条件（对应空值查询）
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"host_list.assets_source", "EXISTS"}}})
		} else {
			// 过滤掉空字符串
			filteredSources := make([]string, 0)
			for _, source := range req.AssetsSource {
				if source != "" {
					filteredSources = append(filteredSources, source)
				}
			}

			if len(filteredSources) > 0 {
				builder.AddMust([]interface{}{"host_list.assets_source", "in", elastic.ToInterfaceArray(filteredSources)})
			}
		}
	}
	// Cloud Name
	if req.CloudName != nil {
		var nameQuery [][]interface{}
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(*req.CloudName)})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToLower(*req.CloudName))})
		nameQuery = append(nameQuery, []interface{}{"cloud_name.keyword", "like", elastic.WithRLAsterisk(strings.ToUpper(*req.CloudName))})
		builder.AddShould(nameQuery...)
	}
	// Clue Company Name
	if len(req.ClueCompanyName) > 0 {
		if utils.ListContains(req.ClueCompanyName, "-") {
			names := utils.ListReplace(req.ClueCompanyName, "-", "")
			builder.AddShould([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(names)})
			builder.AddShould([]interface{}{"MUST_NOT", [][]interface{}{{"clue_company_name", "EXISTS"}}})
		} else {
			builder.AddMust([]interface{}{"clue_company_name", "in", elastic.ToInterfaceArray(req.ClueCompanyName)})
		}
	}
	// 二次确认
	if req.SecondConfirm != nil {
		if *req.SecondConfirm == 1 {
			builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.StatusUploadAsset, fofaee_assets.StatusConfirmAsset}})
			builder.AddShould([]interface{}{"type", fofaee_assets.TYPE_CLAIMED})
			builder.AddShould([]interface{}{"MUST", [][]interface{}{{"second_confirm", 1}, {"type", fofaee_assets.TYPE_RECOMMEND}}})
			builder.AddShould([]interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		} else {
			builder.AddMust([]interface{}{"type", fofaee_assets.TYPE_RECOMMEND})
			builder.AddMustNot([]interface{}{"second_confirm", 1}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		}
	}
	// Not In Clue Domain
	if len(req.NotInClueDomain) > 0 {
		var domainQuery [][]interface{}
		domainQuery = append(domainQuery, []interface{}{"port_list.domain", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
		domainQuery = append(domainQuery, []interface{}{"port_list.subdomain", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
		domainQuery = append(domainQuery, []interface{}{"host_list.domain.keyword", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
		domainQuery = append(domainQuery, []interface{}{"host_list.subdomain.keyword", "in", elastic.ToInterfaceArray(req.NotInClueDomain)})
		builder.AddShould(domainQuery...)
	}

	builder.AddMust([]interface{}{"status", "in", []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}})
	query := builder.Build()
	log.Infof("[IP资产] 查询条件参数: %s", utils.AnyToStr(query))

	// 添加ES查询详细调试信息
	queryParams := elastic.ParseQuery(query)
	queryJSON, _ := json.MarshalIndent(map[string]interface{}{
		"index": "fofaee_assets",
		"body": map[string]interface{}{
			"_source": true,
			"query":   queryParams,
			"sort":    sorts,
			"size":    int(req.PerPage),
			"from":    (int(req.Page) - 1) * int(req.PerPage),
		},
		"track_total_hits": true,
	}, "", "  ")
	log.Infof("【ES查询JSON】: %s", string(queryJSON))

	total, list, err := elastic.ListByParams[fofaee_assets.FofaeeAssets](int(req.Page), int(req.PerPage), query, sorts)
	if err != nil {
		log.Errorf("[IP资产] 查询失败: %v, req: %v", err, req)
		return err
	}

	// 打印查询结果统计
	log.Infof("【ES查询结果】总数: %d, 返回条数: %d, 请求页码: %d, 每页条数: %d", total, len(list), req.Page, req.PerPage)

	// 如果有数据但解析失败，添加详细调试信息
	if total > 0 && len(list) == 0 {
		log.Errorf("【数据解析异常】ES返回了%d条记录但解析后为0条，可能存在数据结构不匹配问题", total)

		// 直接查询原始数据进行调试
		indexName := "fofaee_assets"
		search := elastic.GetEsClient().Search().Index(indexName)
		search = search.SortBy(sorts...)
		search = search.Size(1) // 只取第一条进行调试

		result, debugErr := search.Query(elastic.ParseQuery(query)).Do(context.TODO())
		if debugErr != nil {
			log.Errorf("【调试查询失败】%v", debugErr)
		} else if len(result.Hits.Hits) > 0 {
			// 打印原始JSON数据
			rawJSON := string(*result.Hits.Hits[0].Source)
			log.Errorf("【原始数据调试】文档ID: %s, 索引: %s", result.Hits.Hits[0].Id, result.Hits.Hits[0].Index)
			log.Errorf("【原始JSON数据】: %s", rawJSON)

			// 尝试手动解析到 FofaeeAssets 结构体
			var testAsset fofaee_assets.FofaeeAssets
			parseErr := json.Unmarshal(*result.Hits.Hits[0].Source, &testAsset)
			if parseErr != nil {
				log.Errorf("【手动解析失败】%v", parseErr)
			} else {
				log.Infof("【手动解析成功】资产ID: %s, IP: %s", testAsset.Id, testAsset.Ip)
			}
		}
	}

	// 如果有数据但解析失败，添加详细调试信息
	if total > 0 && len(list) == 0 {
		log.Errorf("【数据解析异常】ES返回了%d条记录但解析后为0条，可能存在数据结构不匹配问题", total)

		// 直接查询原始数据进行调试
		indexName := "fofaee_assets"
		search := elastic.GetEsClient().Search().Index(indexName)
		search = search.SortBy(sorts...)
		search = search.Size(1) // 只取第一条进行调试

		result, debugErr := search.Query(elastic.ParseQuery(query)).Do(context.TODO())
		if debugErr != nil {
			log.Errorf("【调试查询失败】%v", debugErr)
		} else if len(result.Hits.Hits) > 0 {
			// 打印原始JSON数据
			rawJSON := string(*result.Hits.Hits[0].Source)
			log.Errorf("【原始数据调试】文档ID: %s, 索引: %s", result.Hits.Hits[0].Id, result.Hits.Hits[0].Index)
			log.Errorf("【原始JSON数据】: %s", rawJSON)

			// 尝试手动解析到 FofaeeAssets 结构体
			var testAsset fofaee_assets.FofaeeAssets
			parseErr := json.Unmarshal(*result.Hits.Hits[0].Source, &testAsset)
			if parseErr != nil {
				log.Errorf("【手动解析失败】%v", parseErr)

				// 尝试解析到 map[string]interface{} 看看数据结构
				var rawData map[string]interface{}
				mapParseErr := json.Unmarshal(*result.Hits.Hits[0].Source, &rawData)
				if mapParseErr != nil {
					log.Errorf("【Map解析也失败】%v", mapParseErr)
				} else {
					log.Infof("【Map解析成功】数据字段: %v", getMapKeys(rawData))

					// 检查一些关键字段的类型
					checkFieldTypes(rawData)
				}
			} else {
				log.Infof("【手动解析成功】资产ID: %s, IP: %s", testAsset.Id, testAsset.Ip)
			}
		}
	}

	// 处理数据项
	items := make([]map[string]interface{}, 0)
	var processedCount int
	var errorCount int

	// 收集所有资产的group_id，用于查询线索链
	var groupIds []uint64

	for _, item := range list {
		// 添加错误处理，确保单个数据项出错不影响其他数据
		func() {
			defer func() {
				if r := recover(); r != nil {
					errorCount++
					log.Errorf("[IP资产] 处理数据项时发生panic: %v, item_id: %s", r, item.Id)
					return
				}
				processedCount++
			}()

			resultItem := map[string]interface{}{}

			// 处理端口列表
			portList := item.PortList
			if portList != nil {
				// 处理端口详情
				for i, port := range portList {
					func() {
						defer func() {
							if r := recover(); r != nil {
								log.Errorf("[IP资产] 处理端口数据时发生panic: %v, item_id: %s, port_index: %d", r, item.Id, i)
							}
						}()

						// 处理Logo内容，转为链接地址形式
						if port.Logo.Hash != nil && port.Logo.Content != "" {
							portList[i].Logo.Content = storage.GenAPIDownloadPath("", port.Logo.Content, ".ico")
						}

						// 处理HTTP状态码，转为整数
						if port.HttpStatusCode != nil {
							switch v := port.HttpStatusCode.(type) {
							case string:
								code, _ := strconv.Atoi(v)
								portList[i].HttpStatusCode = code
							case float64:
								portList[i].HttpStatusCode = int(v)
							}
						}

						// 处理端口号，转为整数
						if port.Port != nil {
							switch v := port.Port.(type) {
							case float64:
								portList[i].Port = int(v)
							case string:
								port, _ := strconv.Atoi(v)
								portList[i].Port = port
							}
						}
					}()
				}

				// 处理主机列表
				hostList := item.HostList
				if len(hostList) > 0 {
					for i, host := range hostList {
						func() {
							defer func() {
								if r := recover(); r != nil {
									log.Errorf("[IP资产] 处理主机数据时发生panic: %v, item_id: %s, host_index: %d", r, item.Id, i)
								}
							}()

							// 处理Logo内容
							if host.Logo.Hash != nil && host.Logo.Content != "" {
								hostList[i].Logo.Content = storage.GenAPIDownloadPath("", host.Logo.Content, ".ico")
							}

							// 处理HTTP状态码
							if host.HttpStatusCode != nil {
								switch v := host.HttpStatusCode.(type) {
								case string:
									code, _ := strconv.Atoi(v)
									hostList[i].HttpStatusCode = code
								case float64:
									hostList[i].HttpStatusCode = int(v)
								}
							}

							// 处理端口号
							if host.Port != nil {
								switch v := host.Port.(type) {
								case float64:
									hostList[i].Port = int(v)
								case string:
									port, _ := strconv.Atoi(v)
									hostList[i].Port = port
								}
							}

							// 处理域名和子域名，去除协议前缀和端口号
							if host.Domain != "" {
								hostList[i].Domain = cleanDomain(host.Domain)
							}
							if host.Subdomain != "" {
								hostList[i].Subdomain = cleanDomain(host.Subdomain)
							}
						}()
					}
				}

				resultItem["port_list"] = portList
				resultItem["host_list"] = hostList
			}

			// 处理基本信息
			// 处理基本信息
			resultItem["id"] = item.Id
			resultItem["_id"] = item.Id
			resultItem["ip"] = item.Ip
			resultItem["param_ip"] = item.Ip
			resultItem["user_id"] = item.UserId
			resultItem["type"] = item.Type
			resultItem["status"] = item.Status
			resultItem["created_at"] = item.CreatedAt
			resultItem["updated_at"] = item.UpdatedAt
			resultItem["create_time"] = item.CreatedAt
			resultItem["last_update_time"] = item.UpdatedAt
			resultItem["geo"] = item.Geo
			resultItem["latitude"] = item.Geo.Lat
			resultItem["longitude"] = item.Geo.Lon
			resultItem["isp"] = item.Geo.Isp
			resultItem["province"] = item.Geo.Province
			resultItem["asn"] = item.Geo.Asn
			resultItem["clue_company_name"] = item.ClueCompanyName
			resultItem["company_tag_name"] = item.ClueCompanyName
			resultItem["cloud_name"] = item.CloudName
			resultItem["reason_arr"] = item.ReasonArr
			resultItem["customer_tags"] = item.CustomerTags
			resultItem["rule_tags"] = item.RuleTags
			resultItem["tags"] = item.Tags
			resultItem["port_size"] = len(item.PortList)
			resultItem["org_detect_assets_tasks_id"] = item.OrgDetectAssetsTasksId
			resultItem["task_id"] = item.TaskId
			resultItem["is_ipv6"] = item.IsIpv6
			resultItem["is_shadow"] = item.IsShadow
			resultItem["level"] = item.Level
			resultItem["hosts"] = item.Hosts
			resultItem["host_list"] = item.HostList
			resultItem["detect_assets_tasks_id"] = item.DetectAssetsTasksId
			resultItem["online_state"] = item.OnlineState
			resultItem["state"] = getStateText(item.OnlineState)
			resultItem["is_cdn"] = item.IsCdn
			resultItem["website_message_id"] = item.WebsiteMessageId
			resultItem["organization_id"] = item.OrganizationId
			resultItem["reliability_score"] = item.ReliabilityScore
			resultItem["threaten_type"] = item.ThreatenType
			resultItem["port_list_total_page"] = 0    // 默认值，可根据实际情况调整
			resultItem["port_list_per_page_num"] = 30 // 默认值，可根据实际情况调整
			resultItem["rule_infos"] = buildRuleInfos(item.RuleTags)
			resultItem["host_reflect"] = []interface{}{} // 默认空数组
			resultItem["chain_list"] = []interface{}{}   // 默认空数组，后面会被更新
			resultItem["ip_match"] = item.IpMatch
			resultItem["company_match"] = item.CompanyMatch
			resultItem["ip_domain_history"] = []interface{}{}
			if item.IpDomainHistory != nil {
				resultItem["ip_domain_history"] = item.IpDomainHistory
			}
			if item.LateestParseDomain != "" {
				resultItem["lateast_parse_domain"] = item.LateestParseDomain
			}
			if item.LateestParseDomainTime != "" {
				resultItem["lateast_parse_domain_time"] = item.LateestParseDomainTime
			}
			items = append(items, resultItem)

			// 处理ReasonArr，提取group_id
			for _, r := range item.ReasonArr {
				// 直接使用map
				if reasonMap, ok := r.(map[string]interface{}); ok {
					if groupId, ok := reasonMap["group_id"]; ok {
						if cast.ToUint64(groupId) != 0 {
							groupIds = append(groupIds, cast.ToUint64(groupId))
							continue
						}
					}
				}
				// 不是map尝试JSON转换
				var reasonMap map[string]interface{}
				reasonBytes, err := json.Marshal(r)
				if err == nil && json.Unmarshal(reasonBytes, &reasonMap) == nil {
					if groupId, ok := reasonMap["group_id"]; ok {
						if cast.ToUint64(groupId) != 0 {
							groupIds = append(groupIds, cast.ToUint64(groupId))
							continue
						}
					}
				}
			}
		}() // 关闭匿名函数
	}

	// 添加处理统计日志
	log.Infof("【数据处理统计】原始数据: %d, 成功处理: %d, 处理失败: %d, 最终返回: %d", len(list), processedCount, errorCount, len(items))

	// 去重
	groupIds = utils.ListDistinct(groupIds)

	// 处理证据链
	if len(groupIds) > 0 {
		// 如果没有有效的group_id，则跳过线索查询
		if len(groupIds) == 0 {
			log.Infof("[IP资产] 没有有效的group_id，跳过线索查询")
		} else {
			log.Infof("[IP资产] 准备查询线索，有效的group_ids: %v", groupIds)

			// 查询线索表
			clueList, err := cluesModel.NewCluer().ListAll(func(db *gorm.DB) {
				db.Where("user_id = ?", req.UserId)
			})
			if err != nil {
				log.Errorf("[IP资产] 查询线索失败: %v", err)
			} else {
				// 处理每个资产项的证据链
				for i, item := range items {

					// 获取reason_arr
					var reasonArr []interface{}
					if arr, ok := item["reason_arr"].([]interface{}); ok {
						reasonArr = arr
					}

					// 去重reason_arr
					var uniqueReasonArr []interface{}
					reasonMap := make(map[string]bool)
					for _, r := range reasonArr {
						if reasonBytes, err := json.Marshal(r); err == nil {
							key := string(reasonBytes)
							if !reasonMap[key] {
								uniqueReasonArr = append(uniqueReasonArr, r)
								reasonMap[key] = true
							}
						}
					}
					reasonArr = uniqueReasonArr

					// 为每个reason构建线索链
					var chainLists [][]map[string]interface{}
					for _, r := range reasonArr {
						if reasonMap, ok := r.(map[string]interface{}); ok {
							// 获取reason的id
							var reasonID int64
							if id, ok := reasonMap["id"]; ok {
								reasonID = cast.ToInt64(id)
							}

							if reasonID > 0 {
								// 构建线索链
								var chainList []map[string]interface{}
								getTopClue(reasonID, clueList, &chainList)

								if len(chainList) > 0 {
									// 添加当前IP到证据链末尾
									ipStr := fmt.Sprintf("%v", item["ip"])
									chainList = append(chainList, map[string]interface{}{
										"content": ipStr,
									})
									// 添加到结果中
									chainLists = append(chainLists, chainList)
								}
							}
						}
					}

					// 将线索链添加到结果中
					if len(chainLists) > 0 {
						item["chain_list"] = chainLists
					} else {
						log.Infof("[IP资产] 资产项[%d]没有构建出证据链", i)
					}
				}
			}
		}
	}

	// 将结果转为JSON
	jsonData, err := json.Marshal(items)
	if err != nil {
		log.Errorf("[IP资产] 序列化结果失败: %v", err)
		return err
	}

	rsp.Total = total
	rsp.CurrentPage = req.Page
	rsp.PerPage = req.PerPage
	rsp.HasData = total > 0
	rsp.Items = jsonData
	return nil
}

func AssetsAccountIpDelete(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[IP台账资产删除] 开始删除操作，请求参数: %v", req)

	// 第一步：从 fofaee_assets 索引中查询要删除的资产，获取IP列表
	query := ActionSearch(req, []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD})
	log.Infof("[IP台账资产删除] 构建删除查询条件: %s", utils.AnyToStr(query))
	q := elastic.ParseQuery(query)

	// 查询要删除的资产列表
	assets, err := elastic.All[fofaee_assets.FofaeeAssets](500, q, nil, "ip")
	if err != nil {
		log.Errorf("[IP台账资产删除] 查询要删除的资产失败: %v, req: %v", err, req)
		return err
	}

	if len(assets) == 0 {
		log.Infof("[IP台账资产删除] 没有找到要删除的台账资产")
		return nil
	}

	// 提取IP列表
	var ipList []string
	for _, asset := range assets {
		if asset.Ip != "" {
			ipList = append(ipList, asset.Ip)
		}
	}

	log.Infof("[IP台账资产删除] 找到 %d 个台账资产要删除，IP列表: %v", len(assets), ipList)

	// 第二步：删除 fofaee_assets 索引中的数据
	err = elastic.Delete[fofaee_assets.FofaeeAssets](q)
	if err != nil {
		log.Errorf("[IP台账资产删除] 删除 fofaee_assets 中的台账资产失败: %v, req: %v", err, req)
		return err
	}

	log.Infof("[IP台账资产删除] 成功删除 fofaee_assets 中的 %d 个台账资产", len(assets))

	// 第三步：如果有IP列表，删除 foradar_assets 索引中对应的数据
	if len(ipList) > 0 {
		// 构建删除 foradar_assets 的查询条件
		var foradarBuilder elastic.SearchBuilder
		foradarBuilder.AddMust([]interface{}{"user_id", req.UserId})
		foradarBuilder.AddMust([]interface{}{"ip", "in", elastic.ToInterfaceArray(ipList)})

		foradarQuery := elastic.ParseQuery(foradarBuilder.Build())
		log.Infof("[IP台账资产删除] foradar_assets 删除条件: %v", foradarBuilder.Build())

		// 删除 foradar_assets 中的数据
		err = elastic.Delete[foradar_assets.ForadarAsset](foradarQuery)
		if err != nil {
			log.Errorf("[IP台账资产删除] 删除 foradar_assets 失败: %v, IP列表: %v", err, ipList)
			// 这里不返回错误，因为主要的删除操作已经成功
			log.Warnf("[IP台账资产删除] foradar_assets 删除失败，但 fofaee_assets 删除成功")
		} else {
			log.Infof("[IP台账资产删除] 成功删除 foradar_assets 中 %d 个台账资产IP的数据", len(ipList))
		}

		// 第四步：删除IP历史记录数据
		ipHistoryModel := ip_history.NewModel()
		err = ipHistoryModel.DeleteByUserIdAndIps(context.Background(), req.UserId, ipList)
		if err != nil {
			log.Errorf("[IP台账资产删除] 删除台账资产IP历史记录失败: %v, IP列表: %v", err, ipList)
			// 这里不返回错误，因为主要的删除操作已经成功
			log.Warnf("[IP台账资产删除] IP历史记录删除失败，但主要删除操作成功")
		} else {
			log.Infof("[IP台账资产删除] 成功删除 %d 个台账资产IP的历史记录", len(ipList))
		}
	}

	log.Infof("[IP台账资产删除] 删除操作完成，共处理 %d 个台账资产", len(assets))
	return nil
}

// AssetsAccountIpExport 导出IP资产
func AssetsAccountIpExport(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.FileExportResponse) error {
	log.Infof("[IP资产] 导出请求: %v", req)
	query := ActionSearch(req, []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD})
	log.Infof("[IP资产] 查询条件参数: %s", utils.AnyToStr(query))

	q := elastic.ParseQuery(query)
	list, err := elastic.All[fofaee_assets.FofaeeAssets](500, q, GetFofaAssetsDefaultSorter())
	if err != nil {
		log.Errorf("[IP资产] 查询失败: %v, req: %v", err, req)
		return err
	}

	// 生成Excel文件
	file := excelize.NewFile()
	sheet := "IP资产"
	err = file.SetSheetName("Sheet1", sheet)
	if err != nil {
		return err
	}

	// 设置表头
	headers := []string{"IP地址", "企业名称", "端口", "协议", "域名", "URL", "网站标题", "状态码", "云厂商", "组件信息", "地理位置", "资产状态", "资产标签", "发现时间", "更新时间", "icp", "header", "自定义标签", "是否cdn", "资产来源"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err = file.SetCellValue(sheet, cell, header)
		if err != nil {
			return err
		}
	}

	// 写入数据
	row := 2
	for _, r := range list {
		companyName := ""
		if len(r.ClueCompanyName) > 0 {
			companyName, _ = r.ClueCompanyName[0].(string)
		}
		cloudName := ""
		if len(r.CloudName) > 0 {
			cloudName, _ = r.CloudName[0].(string)
		}
		for _, u := range r.HostList {
			if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Ip); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), companyName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("C%d", row), u.Port); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("D%d", row), u.Protocol); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("E%d", row), u.Subdomain); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("F%d", row), u.Url); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("G%d", row), u.Title); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("H%d", row), u.HttpStatusCode); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), cloudName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), GetRuleTagString(r.RuleTags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), r.Geo.Province); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), utils.If(cast.ToInt(u.OnlineState) == 1, "在线", "离线")); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), GetAssetTagString(r.Tags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("N%d", row), r.CreatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("O%d", row), r.UpdatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("P%d", row), u.Icp.No); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("Q%d", row), u.Header); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("R%d", row), strings.Join(r.CustomerTags, ",")); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("S%d", row), utils.If(r.IsCdn, "是", "否")); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			// 处理推荐原因字段
			var reasonStr string
			if len(u.Reason) > 0 {
				reasonStr = getReasonFromAny(u.Reason, r.Type, true, 3) // export=true用于导出
			} else {
				reasonStr = getReasonFromAny(nil, r.Type, true, 3) // export=true用于导出
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("T%d", row), reasonStr); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			row++
		}
		if len(r.HostList) == 0 {
			if err = file.SetCellValue(sheet, fmt.Sprintf("A%d", row), r.Ip); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("B%d", row), companyName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("I%d", row), cloudName); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("J%d", row), GetRuleTagString(r.RuleTags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("K%d", row), r.Geo.Province); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("L%d", row), ""); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("M%d", row), GetAssetTagString(r.Tags)); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("N%d", row), r.CreatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("O%d", row), r.UpdatedAt); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("R%d", row), strings.Join(r.CustomerTags, ",")); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			if err = file.SetCellValue(sheet, fmt.Sprintf("S%d", row), utils.If(r.IsCdn, "是", "否")); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			// 处理推荐原因字段
			var reasonStr string
			reasonStr = "" // export=true用于导出
			if err = file.SetCellValue(sheet, fmt.Sprintf("T%d", row), reasonStr); err != nil {
				return fmt.Errorf("设置单元格值失败: %v", err)
			}
			row++
		}
	}

	// 保存文件
	filename := fmt.Sprintf("ip_assets_%s.xlsx", time.Now().Format("20060102150405"))
	accessPath := filepath.Join(storage.GetPublicStoragePath(), filename)
	storagePath := filepath.Join(storage.GetRootPath(), accessPath)
	if err := file.SaveAs(storagePath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %v", err)
	}

	// 生成前端可访问的路径
	fp, err := utils.DownloadFileEncrypt(accessPath, "IP台账数据"+time.Now().Format(time.DateOnly)+path.Ext(filename), "", false)
	if err != nil {
		return fmt.Errorf("生成下载路径失败: %v", err)
	}
	apiFilePath := filepath.Join(storage.GetDownloadPrefix(), fp+path.Ext(filename))

	// 设置返回的文件路径
	rsp.Url = apiFilePath
	return nil
}

func AssetsAccountIpConfirm(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[IP资产] 确认请求: %v", req)
	req.SecondConfirm = nil
	query := ActionSearch(req, []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD})
	log.Infof("[IP资产] 确认条件参数: %s", utils.AnyToStr(query))
	// 更新状态为已确认
	err := elastic.UpdateByParams[fofaee_assets.FofaeeAssets](query, map[string]interface{}{
		"second_confirm": *req.PushSecondConfirm,
	})
	if err != nil {
		log.Errorf("[IP资产] 确认失败: %v, req: %v", err, req)
		return err
	}
	ipQuery := [][]interface{}{
		{"user_id", req.UserId},
		{"status", fofaee_assets.StatusConfirmAsset},
		{"type", fofaee_assets.TYPE_RECOMMEND},
	}
	q := elastic.ParseQuery(ipQuery)
	ips, err := elastic.All[fofaee_assets.FofaeeAssets](200, q, GetFofaAssetsDefaultSorter())
	if err != nil {
		log.Errorf("[IP资产] 获取关联资产IP失败: %v", err)
		return err
	}
	var ipList []string
	for _, ip := range ips {
		ipList = append(ipList, ip.Ip)
	}
	ipQuery = [][]interface{}{
		{"user_id", req.UserId},
		{"ip", "in", elastic.ToInterfaceArray(ipList)},
	}
	err = elastic.UpdateByParams[foradar_assets.ForadarAsset](ipQuery, map[string]interface{}{
		"second_confirm": *req.PushSecondConfirm,
	})
	return nil
}

func AssetsAccountIpCondition(_ context.Context, req *pb.IpAssetConditionRequest, rsp *pb.IpAssetConditionResponse) error {
	// 使用JSON格式的缓存key
	sureIpCacheKey := fmt.Sprintf("foradar_cache:fofaee_assets_json:sure_ips:%d", req.UserId)
	unSureIpCacheKey := fmt.Sprintf("foradar_cache:fofaee_assets_json:un_sure_ips:%d", req.UserId)

	log.Infof("[IP资产] 缓存key检查 - sure_key: %s, un_sure_key: %s, status: %v",
		sureIpCacheKey, unSureIpCacheKey, req.Status)

	// 尝试获取已确认IP的缓存 (包括已认领和上传的资产)
	if utils.ListContains(req.Status, fofaee_assets.STATUS_CLAIMED) || utils.ListContains(req.Status, fofaee_assets.STATUS_UPLOAD) {
		// 先尝试获取interface{}类型，然后转换为JSON字符串
		var cacheData interface{}
		if redis.GetCache(sureIpCacheKey, &cacheData) {
			log.Infof("[IP资产] 缓存获取成功 - key: %s, type: %T", sureIpCacheKey, cacheData)

			var jsonData string

			// 如果Redis返回的是map，需要重新序列化为JSON
			if mapData, ok := cacheData.(map[string]interface{}); ok {
				jsonBytes, marshalErr := json.Marshal(mapData)
				if marshalErr != nil {
					log.Errorf("[IP资产] 重新序列化缓存数据失败: %v", marshalErr)
				} else {
					jsonData = string(jsonBytes)
					log.Infof("[IP资产] 重新序列化成功，数据长度: %d", len(jsonData))
				}
			} else if strData, ok := cacheData.(string); ok {
				jsonData = strData
				log.Infof("[IP资产] 缓存数据已是字符串格式，长度: %d", len(jsonData))
			} else {
				log.Errorf("[IP资产] 未知的缓存数据类型: %T", cacheData)
			}

			if jsonData != "" {
				if err := json.Unmarshal([]byte(jsonData), rsp); err == nil {
					log.Infof("[IP资产] 获取已确认IP筛选条件 - 缓存命中 (status包含: %v)", req.Status)
					return nil
				} else {
					log.Errorf("[IP资产] 解析已确认IP缓存数据失败: %v", err)
				}
			}
		} else {
			log.Infof("[IP资产] 已确认IP缓存未命中 - key: %s, status: %v", sureIpCacheKey, req.Status)
		}
	}

	// 尝试获取未确认IP的缓存
	if utils.ListContains(req.Status, fofaee_assets.STATUS_DEFAULT) {
		var cacheData interface{}
		if redis.GetCache(unSureIpCacheKey, &cacheData) {
			log.Infof("[IP资产] 未确认IP缓存获取成功 - key: %s, type: %T", unSureIpCacheKey, cacheData)

			var jsonData string

			// 如果Redis返回的是map，需要重新序列化为JSON
			if mapData, ok := cacheData.(map[string]interface{}); ok {
				jsonBytes, marshalErr := json.Marshal(mapData)
				if marshalErr != nil {
					log.Errorf("[IP资产] 重新序列化未确认IP缓存数据失败: %v", marshalErr)
				} else {
					jsonData = string(jsonBytes)
				}
			} else if strData, ok := cacheData.(string); ok {
				jsonData = strData
			}

			if jsonData != "" {
				if err := json.Unmarshal([]byte(jsonData), rsp); err == nil {
					log.Infof("[IP资产] 获取未确认IP筛选条件 - 缓存命中")
					return nil
				} else {
					log.Errorf("[IP资产] 解析未确认IP缓存数据失败: %v", err)
				}
			}
		} else {
			log.Infof("[IP资产] 未确认IP缓存未命中 - key: %s, status: %v", unSureIpCacheKey, req.Status)
		}
	}
	var builder elastic.SearchBuilder
	elastic.StructToParams(req, &builder)
	// 二次确认
	if req.SecondConfirm != nil {
		if *req.SecondConfirm == 1 {
			builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.StatusUploadAsset, fofaee_assets.StatusConfirmAsset}})
			builder.AddShould([]interface{}{"type", fofaee_assets.TYPE_CLAIMED})
			builder.AddShould([]interface{}{"MUST", [][]interface{}{{"second_confirm", 1}, {"type", fofaee_assets.TYPE_RECOMMEND}}})
			builder.AddShould([]interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		} else {
			builder.AddMust([]interface{}{"type", fofaee_assets.TYPE_RECOMMEND})
			builder.AddMustNot([]interface{}{"second_confirm", 1}, []interface{}{"tags", "in", []interface{}{fofaee_assets.SAFE_SCAN, fofaee_assets.CLIENT_SCAN}})
		}
	}
	log.Infof("[IP资产] 获取IP筛选条件请求: %v", builder.Build())
	query := elastic.ParseQuery(builder.Build())
	all, err := elastic.All[fofaee_assets.FofaeeAssets](500, query, GetFofaAssetsDefaultSorter())
	if err != nil {
		log.Errorf("[IP资产] 获取IP筛选条件错误: %v", err)
		return err
	}
	protocolSet := make(map[string]struct{})
	portSet := make(map[int64]struct{})
	onlineStatusSet := make(map[int64]struct{})
	subdomainSet := make(map[string]struct{})
	domainSet := make(map[string]struct{})
	notInClueDomainSet := make(map[string]struct{})
	certSet := make(map[string]struct{})
	icpSet := make(map[string]struct{})
	titleSet := make(map[string]struct{})
	httpStatusCodeSet := make(map[int64]struct{})
	ruleTagSet := make(map[string]struct{})
	cnameSet := make(map[string]struct{})
	asnSet := make(map[string]struct{})
	provinceSet := make(map[string]struct{})
	ispSet := make(map[string]struct{})
	cloudNameSet := make(map[string]struct{})
	customerTagSet := make(map[string]struct{})
	clueCompanyNameSet := make(map[string]struct{})

	clueList, err := cluesModel.NewCluer().ListAll(
		mysql.WithWhere("user_id", req.UserId),
		mysql.WithWhere("status", cluesModel.CLUE_PASS_STATUS),
		mysql.WithWhere("is_deleted", cluesModel.NOT_DELETE),
		mysql.WithWhere("type", cluesModel.TYPE_DOMAIN))
	if err != nil {
		log.Errorf("[IP资产] 获取域名线索列表失败: %v", err)
		return err
	}
	clueDomainSet := make(map[string]struct{})
	for _, c := range clueList {
		clueDomainSet[c.Content] = struct{}{}
	}
	for _, item := range all {
		for _, port := range item.PortList {
			// 协议
			protocolSet[port.Protocol] = struct{}{}
			// 端口
			portSet[cast.ToInt64(port.Port)] = struct{}{}
			// 证书
			certSet[port.Cert.SubjectKey] = struct{}{}
			// Icp
			icpSet[cast.ToString(port.Icp.No)] = struct{}{}
			// HTTP状态码
			httpStatusCodeSet[cast.ToInt64(port.HttpStatusCode)] = struct{}{}
			// CNAME
			for _, cname := range port.Cname {
				cnameSet[cast.ToString(cname)] = struct{}{}
			}
		}
		// 在线状态
		onlineStatusSet[int64(item.OnlineState)] = struct{}{}
		// 子域名
		for _, host := range item.HostList {
			if strings.TrimSpace(host.Subdomain) != "" && host.Subdomain != clues.GetTopDomain(host.Subdomain) {
				subdomainSet[host.Subdomain] = struct{}{}
			}
			// 域名
			if strings.TrimSpace(host.Domain) != "" {
				domainSet[host.Domain] = struct{}{}
				_, ok := clueDomainSet[clues.GetTopDomain(host.Domain)]
				if !ok {
					notInClueDomainSet[host.Domain] = struct{}{}
				}
			}
			// Title
			if strings.TrimSpace(cast.ToString(host.Title)) != "" {
				titleSet[cast.ToString(host.Title)] = struct{}{}
			}
		}
		// Rule Tags
		for _, tag := range item.RuleTags {
			ruleTagSet[tag.CnProduct] = struct{}{}
		}
		// ASN
		if strings.TrimSpace(cast.ToString(item.Geo.Asn)) != "" {
			asnSet[cast.ToString(item.Geo.Asn)] = struct{}{}
		}
		// 省份
		if strings.TrimSpace(cast.ToString(item.Geo.Province)) != "" {
			provinceSet[cast.ToString(item.Geo.Province)] = struct{}{}
		}
		// ISP
		if strings.TrimSpace(cast.ToString(item.Geo.Isp)) != "" {
			ispSet[cast.ToString(item.Geo.Isp)] = struct{}{}
		}
		// 云厂商
		for _, cloud := range item.CloudName {
			if strings.TrimSpace(cast.ToString(cloud)) != "" {
				cloudNameSet[cast.ToString(cloud)] = struct{}{}
			}
		}
		// 自定义标签
		for _, tag := range item.CustomerTags {
			customerTagSet[tag] = struct{}{}
		}
		// 线索企业名称
		for _, name := range item.ClueCompanyName {
			clueCompanyNameSet[cast.ToString(name)] = struct{}{}
		}
	}
	rsp.Protocol = utils.GetMapKeys(protocolSet)
	rsp.Port = utils.GetMapKeys(portSet)
	rsp.OnlineState = utils.GetMapKeys(onlineStatusSet)
	rsp.Subdomain = utils.GetMapKeys(subdomainSet)
	rsp.Domain = append([]string{"-"}, utils.GetMapKeys(domainSet)...)
	rsp.NotInClueDomain = append([]string{"-"}, utils.GetMapKeys(notInClueDomainSet)...)
	rsp.Cert = utils.GetMapKeys(certSet)
	rsp.Icp = utils.GetMapKeys(icpSet)
	rsp.HttpStatusCode = utils.GetMapKeys(httpStatusCodeSet)
	rsp.Title = append([]string{"-"}, utils.GetMapKeys(titleSet)...)
	rsp.RuleTags = utils.GetMapKeys(ruleTagSet)
	rsp.Cname = utils.GetMapKeys(cnameSet)
	rsp.Asn = utils.GetMapKeys(asnSet)
	rsp.Province = utils.GetMapKeys(provinceSet)
	rsp.Isp = utils.GetMapKeys(ispSet)
	rsp.CloudName = utils.GetMapKeys(cloudNameSet)
	rsp.CustomerTags = utils.GetMapKeys(customerTagSet)
	rsp.ClueCompanyName = append([]string{"-"}, utils.GetMapKeys(clueCompanyNameSet)...)

	if utils.ListContains(req.Status, fofaee_assets.STATUS_CLAIMED) {
		redis.SetCache(sureIpCacheKey, 24*time.Hour, rsp)
	}
	if utils.ListContains(req.Status, fofaee_assets.STATUS_DEFAULT) {
		redis.SetCache(unSureIpCacheKey, 24*time.Hour, rsp)
	}
	return nil
}

// ip端口维度资产高级筛选
func IpPortAssetCondition(_ context.Context, req *pb.IpPortAssetConditionRequest, rsp *pb.IpPortAssetConditionResponse) error {

	// 使用JSON格式的缓存key
	sureIpCacheKey := fmt.Sprintf("foradar_cache:foradar_assets_json:sure_ips:%d", req.UserId)
	unSureIpCacheKey := fmt.Sprintf("foradar_cache:foradar_assets_json:un_sure_ips:%d", req.UserId)

	log.Infof("[IP端口资产] 缓存key检查 - sure_key: %s, un_sure_key: %s, status: %v",
		sureIpCacheKey, unSureIpCacheKey, req.Status)

	// 调试：检查Redis中是否存在相关的key
	var testSure, testUnSure interface{}
	sureExists := redis.GetCache(sureIpCacheKey, &testSure)
	unSureExists := redis.GetCache(unSureIpCacheKey, &testUnSure)
	log.Infof("[IP端口资产] Redis key存在性检查 - sure_key存在: %v, un_sure_key存在: %v", sureExists, unSureExists)

	// 尝试获取已确认IP的缓存 (包括已认领和上传的资产)
	if utils.ListContains(req.Status, fofaee_assets.STATUS_CLAIMED) || utils.ListContains(req.Status, fofaee_assets.STATUS_UPLOAD) {
		var cacheData interface{}
		if redis.GetCache(sureIpCacheKey, &cacheData) {
			log.Infof("[IP端口资产] 缓存获取成功 - key: %s, type: %T", sureIpCacheKey, cacheData)

			var jsonData string

			// 如果Redis返回的是map，需要重新序列化为JSON
			if mapData, ok := cacheData.(map[string]interface{}); ok {
				jsonBytes, marshalErr := json.Marshal(mapData)
				if marshalErr != nil {
					log.Errorf("[IP端口资产] 重新序列化缓存数据失败: %v", marshalErr)
				} else {
					jsonData = string(jsonBytes)
					log.Infof("[IP端口资产] 重新序列化成功，数据长度: %d", len(jsonData))
				}
			} else if strData, ok := cacheData.(string); ok {
				jsonData = strData
				log.Infof("[IP端口资产] 缓存数据已是字符串格式，长度: %d", len(jsonData))
			} else {
				log.Errorf("[IP端口资产] 未知的缓存数据类型: %T", cacheData)
			}

			if jsonData != "" {
				if err := json.Unmarshal([]byte(jsonData), rsp); err == nil {
					log.Infof("[IP端口资产] 获取已确认IP筛选条件 - 缓存命中 (status包含: %v)", req.Status)
					return nil
				} else {
					log.Errorf("[IP端口资产] 解析已确认IP缓存数据失败: %v", err)
				}
			}
		} else {
			log.Infof("[IP端口资产] 已确认IP缓存未命中 - key: %s, status: %v", sureIpCacheKey, req.Status)
		}
	}

	// 尝试获取未确认IP的缓存
	if utils.ListContains(req.Status, fofaee_assets.STATUS_DEFAULT) {
		var cacheData interface{}
		if redis.GetCache(unSureIpCacheKey, &cacheData) {
			log.Infof("[IP端口资产] 未确认IP缓存获取成功 - key: %s, type: %T", unSureIpCacheKey, cacheData)

			var jsonData string

			// 如果Redis返回的是map，需要重新序列化为JSON
			if mapData, ok := cacheData.(map[string]interface{}); ok {
				jsonBytes, marshalErr := json.Marshal(mapData)
				if marshalErr != nil {
					log.Errorf("[IP端口资产] 重新序列化未确认IP缓存数据失败: %v", marshalErr)
				} else {
					jsonData = string(jsonBytes)
				}
			} else if strData, ok := cacheData.(string); ok {
				jsonData = strData
			}

			if jsonData != "" {
				if err := json.Unmarshal([]byte(jsonData), rsp); err == nil {
					log.Infof("[IP端口资产] 获取未确认IP筛选条件 - 缓存命中")
					return nil
				} else {
					log.Errorf("[IP端口资产] 解析未确认IP缓存数据失败: %v", err)
				}
			}
		} else {
			log.Infof("[IP端口资产] 未确认IP缓存未命中 - key: %s, status: %v", unSureIpCacheKey, req.Status)

			// 尝试直接查询Redis来确认key是否存在
			var testData interface{}
			if redis.GetCache(unSureIpCacheKey, &testData) {
				log.Infof("[IP端口资产] 直接查询Redis成功，但类型可能不匹配: %T, 数据: %v", testData, testData)
			} else {
				log.Infof("[IP端口资产] 直接查询Redis也失败，key确实不存在")
			}
		}
	}
	var builder elastic.SearchBuilder
	elastic.StructToParams(req, &builder)
	log.Infof("[IP资产] 获取IP筛选条件请求: %v", builder.Build())
	query := elastic.ParseQuery(builder.Build())
	all, err := elastic.All[foradar_assets.ForadarAsset](500, query, nil)
	if err != nil {
		log.Errorf("[IP资产] 获取IP筛选条件错误: %v", err)
		return err
	}
	protocolSet := make(map[string]struct{})
	portSet := make(map[int64]struct{})
	onlineStatusSet := make(map[int64]struct{})
	subdomainSet := make(map[string]struct{})
	domainSet := make(map[string]struct{})
	notInClueDomainSet := make(map[string]struct{})
	certSet := make(map[string]struct{})
	icpSet := make(map[string]struct{})
	titleSet := make(map[string]struct{})
	httpStatusCodeSet := make(map[int64]struct{})
	ruleTagSet := make(map[string]struct{})
	cnameSet := make(map[string]struct{})
	asnSet := make(map[string]struct{})
	provinceSet := make(map[string]struct{})
	ispSet := make(map[string]struct{})
	urlSet := make(map[string]struct{})
	// cloudNameSet 和 customerTagSet 在 foradar_assets 中不存在，所以删除
	clueCompanyNameSet := make(map[string]struct{})

	clueList, err := cluesModel.NewCluer().ListAll(
		mysql.WithWhere("user_id", req.UserId),
		mysql.WithWhere("status", cluesModel.CLUE_PASS_STATUS),
		mysql.WithWhere("is_deleted", cluesModel.NOT_DELETE),
		mysql.WithWhere("type", cluesModel.TYPE_DOMAIN))
	if err != nil {
		log.Errorf("[IP资产] 获取域名线索列表失败: %v", err)
		return err
	}
	clueDomainSet := make(map[string]struct{})
	for _, c := range clueList {
		clueDomainSet[c.Content] = struct{}{}
	}
	for _, item := range all {
		// 协议
		if item.Protocol != "" {
			protocolSet[item.Protocol] = struct{}{}
		}
		if item.Url != "" {
			urlSet[item.Url] = struct{}{}
		}
		// 端口
		if port := cast.ToInt64(item.Port); port > 0 {
			portSet[port] = struct{}{}
		}
		// 证书
		if item.Cert.SubjectKey != "" {
			certSet[item.Cert.SubjectKey] = struct{}{}
		}
		// ICP
		if item.Icp.No != "" {
			icpSet[item.Icp.No] = struct{}{}
		}
		// HTTP状态码
		if code := cast.ToInt64(item.HTTPStatusCode); code > 0 {
			httpStatusCodeSet[code] = struct{}{}
		}
		// CNAME
		for _, cname := range item.Cname {
			if cnameStr := cast.ToString(cname); cnameStr != "" {
				cnameSet[cnameStr] = struct{}{}
			}
		}
		// 在线状态
		if state := cast.ToInt64(item.OnlineState); state >= 0 {
			onlineStatusSet[state] = struct{}{}
		}
		// 子域名
		if strings.TrimSpace(item.Subdomain) != "" && item.Subdomain != clues.GetTopDomain(item.Subdomain) {
			subdomainSet[item.Subdomain] = struct{}{}
		}
		// 域名
		if strings.TrimSpace(item.Domain) != "" {
			domainSet[item.Domain] = struct{}{}
			_, ok := clueDomainSet[clues.GetTopDomain(item.Domain)]
			if !ok {
				notInClueDomainSet[item.Domain] = struct{}{}
			}
		}
		// Title
		if title := cast.ToString(item.Title); strings.TrimSpace(title) != "" {
			titleSet[title] = struct{}{}
		}
		// Rule Tags
		for _, tag := range item.RuleTags {
			if tag.CnProduct != "" {
				ruleTagSet[tag.CnProduct] = struct{}{}
			}
		}
		// ASN
		if asn := cast.ToString(item.Geo.Asn); strings.TrimSpace(asn) != "" && asn != "0" {
			asnSet[asn] = struct{}{}
		}
		// 省份
		if strings.TrimSpace(item.Geo.Province) != "" {
			provinceSet[item.Geo.Province] = struct{}{}
		}
		// ISP
		if strings.TrimSpace(item.Geo.Isp) != "" {
			ispSet[item.Geo.Isp] = struct{}{}
		}
		// 线索企业名称 - 处理 any 类型
		if item.ClueCompanyName != nil {
			if nameStr, ok := item.ClueCompanyName.(string); ok && nameStr != "" {
				clueCompanyNameSet[nameStr] = struct{}{}
			} else if nameArray, ok := item.ClueCompanyName.([]interface{}); ok {
				for _, name := range nameArray {
					if nameStr := cast.ToString(name); nameStr != "" {
						clueCompanyNameSet[nameStr] = struct{}{}
					}
				}
			}
		}
	}
	rsp.Protocol = utils.GetMapKeys(protocolSet)
	rsp.Port = utils.GetMapKeys(portSet)
	rsp.OnlineState = utils.GetMapKeys(onlineStatusSet)
	rsp.Subdomain = utils.GetMapKeys(subdomainSet)
	rsp.Domain = append([]string{"-"}, utils.GetMapKeys(domainSet)...)
	rsp.NotInClueDomain = append([]string{"-"}, utils.GetMapKeys(notInClueDomainSet)...)
	rsp.Cert = utils.GetMapKeys(certSet)
	rsp.Icp = utils.GetMapKeys(icpSet)
	rsp.HttpStatusCode = utils.GetMapKeys(httpStatusCodeSet)
	rsp.Title = append([]string{"-"}, utils.GetMapKeys(titleSet)...)
	rsp.RuleTags = utils.GetMapKeys(ruleTagSet)
	rsp.Cname = utils.GetMapKeys(cnameSet)
	rsp.Asn = utils.GetMapKeys(asnSet)
	rsp.Province = utils.GetMapKeys(provinceSet)
	rsp.Isp = utils.GetMapKeys(ispSet)
	rsp.Url = append([]string{"-"}, utils.GetMapKeys(urlSet)...)
	// foradar_assets 没有 CloudName 和 CustomerTags 字段，所以不设置这些值
	rsp.ClueCompanyName = append([]string{"-"}, utils.GetMapKeys(clueCompanyNameSet)...)
	if utils.ListContains(req.Status, fofaee_assets.STATUS_CLAIMED) {
		redis.SetCache(sureIpCacheKey, 24*time.Hour, rsp)
	}
	if utils.ListContains(req.Status, fofaee_assets.STATUS_DEFAULT) {
		redis.SetCache(unSureIpCacheKey, 24*time.Hour, rsp)
	}
	return nil
}

func AssetsAccountSetCustomerTag(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[IP资产] 设置自定义标签请求: %v", req)
	tags := req.CustomerTags
	req.CustomerTags = nil
	search := ActionSearch(req, []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD})
	log.Infof("[IP资产] 设置自定义标签查询条件: %s", utils.AnyToStr(search))
	err := elastic.UpdateByParams[fofaee_assets.FofaeeAssets](search, map[string]interface{}{
		"customer_tags": tags,
	})
	if err != nil {
		log.Errorf("[IP资产] 设置自定义标签失败: %v, req: %v", err, req)
		return err
	}
	return nil
}

// cleanDomain 清理域名，去除协议前缀和端口号
func cleanDomain(domain string) string {
	// 去掉协议前缀
	if strings.HasPrefix(domain, "http://") {
		domain = domain[7:]
	} else if strings.HasPrefix(domain, "https://") {
		domain = domain[8:]
	}

	// 去掉端口号
	if idx := strings.Index(domain, ":"); idx > 0 {
		domain = domain[:idx]
	}

	return domain
}

// getTopClue 获取线索链，递归查找父级线索
func getTopClue(parentID int64, clueList []*cluesModel.Clue, chainList *[]map[string]interface{}, id ...int64) {
	var currentID int64
	if len(id) > 0 {
		currentID = id[0]
	}

	for _, clue := range clueList {
		// 处理初始查找（通过ID查找）
		if parentID == 0 && clue.Id == uint64(currentID) {
			// 如果是推荐来源且有from_ip
			if clue.Source == cluesModel.SOURCE_RECOMMEND && clue.FromIp != "" {
				// 如果不是首级线索，将from_ip添加到末尾
				if clue.ParentId > 0 {
					*chainList = append(*chainList, map[string]interface{}{
						"content": clue.FromIp,
					})
				} else {
					// 否则添加到开头
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
				}
			}
		} else {
			// 通过父ID查找
			if clue.Id == uint64(parentID) {
				// 将当前线索添加到链的开头
				*chainList = append([]map[string]interface{}{
					{
						"id":                clue.Id,
						"content":           clue.Content,
						"type":              clue.Type,
						"parent_id":         clue.ParentId,
						"clue_company_name": clue.ClueCompanyName,
						"source":            clue.Source,
					},
				}, *chainList...)
				// 如果是推荐来源且有from_ip
				if clue.Source == cluesModel.SOURCE_RECOMMEND && clue.FromIp != "" {
					*chainList = append([]map[string]interface{}{
						{"content": clue.FromIp},
					}, *chainList...)
				}

				// 递归查找父级线索
				getTopClue(int64(clue.ParentId), clueList, chainList, currentID)
			}
		}
	}
}

// getReasonFromAny 处理FofaeeAssetPort的Reason字段（[]any类型），转换为字符串
func getReasonFromAny(reason []fofaee_assets.AssetReason, assetType int, export bool, typeTwo int) string {
	// 对应PHP: if (\App\Models\Elastic\IpAssets::TYPE_CLAIMED === $type || \App\Models\Elastic\IpAssets::STATUS_UPLOAD === $type)
	if assetType == fofaee_assets.TYPE_CLAIMED || assetType == fofaee_assets.STATUS_UPLOAD {
		return "已知资产ip"
	}

	// 对应PHP: if (\App\Models\Elastic\IpAssets::TYPE_CLAIMED === $type_two || \App\Models\Elastic\IpAssets::STATUS_UPLOAD === $type_two)
	if typeTwo == fofaee_assets.TYPE_CLAIMED || typeTwo == fofaee_assets.STATUS_UPLOAD {
		return "已知资产ip"
	}

	if len(reason) == 0 {
		return ""
	}

	var info strings.Builder

	// 对应PHP的foreach ($reason as $val)
	for _, val := range reason {
		clueCompanyName := val.ClueCompanyName
		if clueCompanyName == "" {
			clueCompanyName = "-"
		}

		// 对应PHP: if (($val['type'] ?? '') == \App\Models\MySql\Clue::TYPE_LOGO)
		if val.Type == cluesModel.TYPE_LOGO {
			var icoRecTxt string
			if export {
				// 对应PHP的导出模式
				icoRecTxt = storage.GenDownloadUrl(val.Content, "") + " 推荐;"
			} else {
				// 对应PHP的HTML模式
				// 检查content是否包含"pp/public"
				if strings.Contains(val.Content, "pp/public") {
					icoRecTxt = fmt.Sprintf(`<img class="reason_ico" src="%s" />推荐;`, storage.GenDownloadUrl(val.Content, ""))
				} else {
					icoRecTxt = fmt.Sprintf(`<img class="reason_ico" src="%s" />推荐;`, val.Content)
				}
			}
			// 对应PHP: $info .= '根据' . $clue_company_name . '的' . typeToCn($val['type'] ?? '') .' '. $icoRecTxt;
			info.WriteString(fmt.Sprintf("根据%s的%s %s", clueCompanyName, utils.TypeToCn(val.Type), icoRecTxt))
		} else {
			// 对应PHP: $info .= '根据' . $clue_company_name . '的' . typeToCn($val['type'] ?? '') .' '. $val['content'] . '推荐;';
			info.WriteString(fmt.Sprintf("根据%s的%s %s推荐;", clueCompanyName, utils.TypeToCn(val.Type), val.Content))
		}
	}

	return info.String()
}

func AssetsAccountSetStatus(_ context.Context, req *pb.IpAssetActionRequest, rsp *pb.Empty) error {
	log.Infof("[IP资产] 设置状态请求: %v", req)
	update := make(map[string]any)
	if req.SetStatus == nil {
		return fmt.Errorf("缺少要设置的状态")
	}
	switch *req.SetStatus {
	case fofaee_assets.STATUS_DEFAULT:
		update["status"] = fofaee_assets.STATUS_DEFAULT
		update["type"] = fofaee_assets.TYPE_RECOMMEND
		update["is_user_sign_unsure"] = 1
	case fofaee_assets.STATUS_CLAIMED:
		update["status"] = fofaee_assets.STATUS_CLAIMED
	case fofaee_assets.STATUS_IGNORE:
		update["status"] = fofaee_assets.STATUS_IGNORE
	case fofaee_assets.STATUS_THREATEN:
		update["status"] = fofaee_assets.STATUS_THREATEN
		if req.ThreatenType == nil {
			return fmt.Errorf("设置威胁类型时缺少威胁类型")
		}
		update["threaten_type"] = *req.ThreatenType
		if req.ThreatenTypeName == nil {
			update["threaten_type_name"] = "其他"
		}
		update["threaten_type_name"] = *req.ThreatenTypeName
	default:
		return fmt.Errorf("不支持的状态: %d", *req.SetStatus)
	}
	update["is_shadow"] = fofaee_assets.NOT_SHADOW
	param := ActionSearch(req, []any{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD})
	query := elastic.ParseQuery(param)
	all, err := elastic.All[fofaee_assets.FofaeeAssets](500, query, GetFofaAssetsDefaultSorter(), "ip", "organization_id")
	if err != nil {
		log.Errorf("[IP资产] 获取IP资产失败: %v", err)
		return err
	}
	ipList := utils.ListColumn(all, func(t *fofaee_assets.FofaeeAssets) string { return t.Ip })
	var orgList []int64
	for _, item := range all {
		for _, id := range item.OrganizationId {
			orgList = append(orgList, cast.ToInt64(id))
		}
	}
	orgList = utils.ListDistinctNonZero(orgList)
	log.Infof("[IP资产] 更新资产状态查询条件: %s", utils.AnyToStr(param))
	err = elastic.UpdateByParams[fofaee_assets.FofaeeAssets](param, update)
	if err != nil {
		log.Errorf("[IP资产] 更新Fofa资产失败: %v, req: %v", err, req)
		return err
	}
	param = [][]interface{}{
		{"user_id", req.UserId},
		{"ip", "in", elastic.ToInterfaceArray(ipList)},
	}
	if len(ipList) > 0 {
		log.Infof("[IP资产] 更新Foradar资产查询条件: %s", utils.AnyToStr(param))
		err = elastic.UpdateByParams[foradar_assets.ForadarAsset](param, update)
		if err != nil {
			log.Errorf("[IP资产] 更新Foradar资产失败: %v, req: %v", err, req)
			return err
		}
	} else {
		return nil
	}
	if req.CompanyId != 0 {
		allUser, err := user.NewUserModel().ListAll(mysql.WithWhere("company_id", req.CompanyId))
		if err != nil {
			log.Errorf("[IP资产] 获取公司用户失败: %v, req: %v", err, req)
			return err
		}
		ids := utils.ListColumn(allUser, func(t user.User) uint64 { return t.Id })
		var builder elastic.SearchBuilder
		builder.AddShould([]interface{}{"user_id", "in", ids})
		builder.AddShould([]interface{}{"company_id", req.CompanyId})
		builder.AddMust([]interface{}{"status", "in", []interface{}{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}})
		total, err := elastic.CountByParams[fofaee_assets.FofaeeAssets](builder.Build())
		if err != nil {
			log.Errorf("[IP资产] 获取Fofa资产数量失败: %v, req: %v", err, req)
		}
		comp, err := company.NewCompanyModel().FindById(req.CompanyId, req.UserId)
		if err != nil {
			log.Errorf("[IP资产] 获取公司信息失败: %v, req: %v", err, req)
		}
		err = comp.UpdateLimit(company.LIMIT_TYPE_IP, int(total), false)
		if err != nil {
			log.Errorf("[IP资产] 更新公司IP资产数量限制失败: %v, req: %v", err, req)
		}
	}
	if cfg.ExecGolangJob() {
		//todo 调用go的job
		err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
		if err != nil {
			log.Errorf("[IP资产] 分发缓存IP条件任务失败: %v, req: %v", err, req)
		}
	} else {
		err = asyncq.CacheTableIpsConditionJob.Dispatch(req.UserId)
		if err != nil {
			log.Errorf("[IP资产] 分发缓存IP条件任务失败: %v, req: %v", err, req)
		}
	}

	return nil
}

// getStateText 获取状态文本
func getStateText(onlineState int) string {
	if onlineState == 1 {
		return "在线"
	}
	return "离线"
}

// buildRuleInfos 根据PHP逻辑构建rule_infos字段
// 对应PHP: $rules = implode(',', array_unique(array_column($rules, 'cn_product')));
func buildRuleInfos(ruleTags []fofaee_assets.RuleTag) string {
	if len(ruleTags) == 0 {
		return ""
	}

	// 提取cn_product字段 - 对应PHP的array_column($rules, 'cn_product')
	var products []string
	for _, tag := range ruleTags {
		if tag.CnProduct != "" {
			products = append(products, tag.CnProduct)
		}
	}

	// 去重 - 对应PHP的array_unique
	uniqueProducts := make(map[string]bool)
	var uniqueList []string
	for _, product := range products {
		if !uniqueProducts[product] {
			uniqueProducts[product] = true
			uniqueList = append(uniqueList, product)
		}
	}

	// 用逗号连接 - 对应PHP的implode(',', ...)
	return strings.Join(uniqueList, ",")
}

// minInt64 辅助函数
func minInt64(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}
