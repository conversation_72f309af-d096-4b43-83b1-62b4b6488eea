package fofaee_assets

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"

	es "micro-service/middleware/elastic"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"

	jsoniter "github.com/json-iterator/go"
	"github.com/olivere/elastic"
	"github.com/spf13/cast"
)

// FofaeeAssetsIndex IP维度fofa资产
const FofaeeAssetsIndex = "fofaee_assets"

const FofaeeAssetsType = "ips"

const (
	StatusSuspectedAsset = iota // 疑似资产
	StatusConfirmAsset          // 已确认资产
	StatusIgnoreAsset           // 忽略资产
	StatusThreatAsset           // 威胁资产
	StatusUploadAsset           // 导入资产
)

// 资产状态常量
const (
	STATUS_DEFAULT  = 0 // 默认状态/未知资产
	STATUS_CLAIMED  = 1 // 已认领资产
	STATUS_IGNORE   = 2 // 上传资产
	STATUS_THREATEN = 3 // 威胁资产
	STATUS_UPLOAD   = 4
)

// 资产类型常量
const (
	TYPE_CLAIMED   = 0
	TYPE_RECOMMEND = 1 // 推荐资产
)

// 资产标签常量
const (
	CLIENT_SCAN   = 0 // 客户扫描
	SAFE_SCAN     = 1 // 安服扫描
	CLIENT_REC    = 2 // 客户推荐
	SAFE_REC      = 3 // 安服推荐
	CLIENT_IMPORT = 4 // 安服导入
)

// 域名对应的实时解析ip与该资产的ip的匹配情况  1/匹配 2/不匹配
const (
	DOMAIN_PARSE_MATCH     = 1
	DOMAIN_NOT_PARSE_MATCH = 2

	// 域名ICP匹配状态
	DOMAIN_ICP_MATCH     = 1
	DOMAIN_NOT_ICP_MATCH = 2
)

// StatusAccount 资产台账状态
var StatusAccount = []int{StatusConfirmAsset, StatusUploadAsset}

const (
	ThreatenTypeOther  = iota // 威胁类型: 其他
	ThreatenTypeDY            // 威胁类型: 钓鱼
	ThreatenTypeHDD           // 威胁类型: 黄赌毒
	ThreatenTypeFmICP         // 威胁类型: 仿冒ICP
	ThreatenTypeDomain        // 威胁类型: 域名混淆
)

const (
	StatusOffline = iota // 离线
	StatusOnline         // 在线
)

const (
	TagUserScan      = iota // 用户扫描
	TagSafeScan             // 安服扫描
	TagUserRecommend        // 用户推荐
	TagSafeRecommend        // 安服推荐
)

// 是否是影子资产
const (
	IS_SHADOW  = 1
	NOT_SHADOW = 2
)

type (
	FofaeeAssetsModel interface {
		FindByIpAndPort(userId int, list []es.IpPort, status ...int) ([]FofaeeAssets, error)
		FindByCondition(ctx context.Context, param *FindCondition, page, size int, fields ...string) ([]*FofaeeAssets, int64, error)
		FindById(ctx context.Context, id string) *AssetInfo
		FindFullById(ctx context.Context, id string) *FofaeeAssets // 新增方法，返回完整的FofaeeAssets
		CountIpByUserTask(userID int, taskIds []uint) (int, error)
		CountRuleByUserTask(userID int, taskIds []uint) (int, error)
		CountByStatus(int, []int) (int, error)
		Count(ctx context.Context, query *elastic.BoolQuery) (int64, error)
		GroupByStatusWithDetectTask(userId uint, detectAssetTasks []uint) ([]FofaeeAssetsGroupByStatus, error)
		AggByRule(userID uint64, size int) ([]CountData, error)
		CountByStatusAndUserIDAndRange(userID int, status []any, start, end string) (int64, error)
		Updates(context.Context, ...*FofaeeAssets) error
		Create(context.Context, []*FofaeeAssets) ([]string, []string, error)
		UpdatePorts(context.Context, map[string]UpdatePortsHost) error // map key is doc id
		DeleteByTaskId(ctx context.Context, taskId uint64) error
		FindUserIdsAndIpByTaskId(ctx context.Context, taskId uint64) ([]*FofaeeAssets, error)
	}

	defaultFofaeeAssetsModel struct {
		*elastic.Client
		Type  string
		Index string
	}

	countDataBuckets struct {
		DocCountErrorUpperBound int         `json:"doc_count_error_upper_bound"`
		SumOtherDocCount        int         `json:"sum_other_doc_count"`
		Buckets                 []CountData `json:"buckets"`
	}

	CountData struct {
		Name  string `json:"key"`
		Count int64  `json:"doc_count"`
	}

	FofaeeAssets struct {
		Id                     string            `json:"id"` // user_id + "_"+ ip
		PortSize               int               `json:"port_size"`
		TaskId                 []int             `json:"task_id"`                    // 扫描任务
		OrgDetectAssetsTasksId []int             `json:"org_detect_assets_tasks_id"` // 组织检测资产任务ID
		IsIpv6                 bool              `json:"is_ipv6"`
		Type                   int               `json:"type"`                      // 1
		ClueCompanyName        []any             `json:"clue_company_name"`         // []string
		IpDomainHistory        []IpDomainHistory `json:"ip_domain_history"`         // IP域名历史记录
		LateestParseDomainTime string            `json:"lateast_parse_domain_time"` // 最新解析域名时间
		LateestParseDomain     string            `json:"lateast_parse_domain"`      // 最新解析域名
		Geo                    Geo               `json:"geo"`
		RiskType               []int             `json:"risk_type"`
		PortList               []FofaeeAssetPort `json:"port_list"`
		HostList               []FofaeeAssetPort `json:"host_list"`
		Level                  any               `json:"level"` // default:4
		Hosts                  []string          `json:"hosts"` //
		CloudName              []any             `json:"cloud_name"`
		Ip                     string            `json:"ip"`
		Tags                   []int             `json:"tags"` // 资产标签
		ReasonArr              []any             `json:"reason_arr"`
		DetectAssetsTasksId    []int             `json:"detect_assets_tasks_id"` // 资产推荐
		RuleTags               []RuleTag         `json:"rule_tags"`
		IpMatch                int               `json:"ip_match"`     // IP匹配
		OnlineState            int               `json:"online_state"` // 资产在线状态: 0离线 1在线
		UserId                 int               `json:"user_id"`
		IsCdn                  bool              `json:"is_cdn"`             //
		WebsiteMessageId       int               `json:"website_message_id"` // 网站消息ID
		OrganizationId         []any             `json:"organization_id"`    // 组织ID
		ReliabilityScore       int               `json:"reliability_score"`
		ThreatenType           any               `json:"threaten_type"`       // int
		ThreatenTypeName       any               `json:"threaten_type_name"`  // string
		CompanyMatch           int               `json:"company_match"`       // 公司匹配
		Status                 any               `json:"status"`              // int
		IsShadow               any               `json:"is_shadow"`           // int: 1-是 2-不是
		IsUserSignUnsure       int               `json:"is_user_sign_unsure"` // int: 用户标记为疑似状态
		AllTitle               any               `json:"all_title"`           // []string
		AllDomain              any               `json:"all_domain"`          // []string
		CreatedAt              string            `json:"created_at"`          // time.Time to string with format "2006-01-02 15:04:05"
		UpdatedAt              string            `json:"updated_at"`          // same as created_at
		CustomerTags           []string          `json:"customer_tags"`       // 自定义标签
	}

	Geo struct {
		Continent any    `json:"continent"`
		Zip       any    `json:"zip"`
		Country   string `json:"country"`
		City      any    `json:"city"`
		Org       any    `json:"org"`
		Isp       any    `json:"isp"`
		Lon       any    `json:"lon"` // float64
		As        any    `json:"as"`
		Province  string `json:"province"`
		District  any    `json:"district"`
		Asn       any    `json:"asn"`
		Lat       any    `json:"lat"` // float64
		AsName    any    `json:"as_name"`
	}

	IpDomainHistory struct {
		FoundTime  string `json:"found_time"`  // 发现时间
		Domain     string `json:"domain"`      // 域名
		Source     int    `json:"source"`      // 来源
		IcpCompany string `json:"icp_company"` // ICP公司
	}

	FofaeeAssetPort struct {
		Fid                string        `json:"fid"`
		Reason             []AssetReason `json:"reason"`
		ReasonArr          []AssetReason `json:"reason_arr"`
		HttpStatusCode     any           `json:"http_status_code"` // type: int
		IsOpen             any           `json:"is_open"`          // type: int, 1-在线, 0-离线
		OnlineState        any           `json:"online_state"`     // type: int, 1-在线, 0-离线
		Cname              []any         `json:"cname"`
		AssetsSource       int           `json:"assets_source,omitempty"` // 资产来源 1/FOFA的search/all接口返回的 2/IP138 3/chaziyu 4/hunter 5/线索库域名实时解析 6/FOFA纯解析域名数据 7/扫描任务中域名实时解析出来的，而且不在线索库中的域名 8/列表端口扫描的时候，某个端口上扫出来新的协议数据，类似基础扫描类型
		AssetsSourceDomain string        `json:"assets_source_domain,omitempty"`
		SourceUpdatedAt    string        `json:"source_updated_at,omitempty"` // 源更新时间
		OneforallSource    string        `json:"oneforall_source,omitempty"`  // OneForAll来源
		Banner             string        `json:"banner,omitempty"`
		Cert               any `json:"cert"` // 可以是对象或数组，兼容PHP格式
		Screenshot      any    `json:"screenshot"`
		Title           any    `json:"title"` // type: string
		Url             string `json:"url"`
		ClueCompanyName []any  `json:"clue_company_name"` // should be []string
		UrlArr          []any  `json:"url_arr"`
		Protocol        string `json:"protocol"`
		Port            any    `json:"port"` // type: int
		Domain          string `json:"domain"`
		Icp             struct {
			Date        any `json:"date"`
			No          any `json:"no"`
			CompanyName any `json:"company_name"`
			Type        any `json:"type"`
		} `json:"icp"`
		ReliabilityScore int    `json:"reliability_score"`
		Header           string `json:"header"`
		Subdomain        string `json:"subdomain"`
		Logo             Icon   `json:"logo"`
		IsLogin          any    `json:"is_login"`
		OpenParse        bool   `json:"open_parse"`
		RiskType         []any  `json:"risk_type"`
	}

	AssetReason struct {
		Id              int    `json:"id"`
		Type            int    `json:"type"`
		Content         string `json:"content"`
		GroupId         int    `json:"group_id"`
		ClueCompanyName string `json:"clue_company_name"`
		Source          int    `json:"source"`
	}

	RuleTag struct {
		CnProduct        string `json:"cn_product"`
		RuleId           string `json:"rule_id"`
		Product          string `json:"product"`
		CnCategory       string `json:"cn_category"`
		Level            string `json:"level"`
		ParentCategory   string `json:"parent_category"`
		Softhard         string `json:"softhard"`
		Company          string `json:"company"`
		CnParentCategory string `json:"cn_parent_category"`
		Category         string `json:"category"`
		CnCompany        string `json:"cn_company"`
		PortArr          []any  `json:"port_arr"` // 端口数组
	}
)

type Icon struct {
	Hash    any    `json:"hash"` // int
	Content string `json:"content"`
}

type UpdatePortsHost struct {
	List            []FofaeeAssetPort
	HostList        []FofaeeAssetPort
	Hosts           []string
	Status          int
	ClueCompanyName []any
	OnlineState     int
	RuleTags        []RuleTag
	Tags            []int // fofaee_assets.tags
}

type AssetInfo struct {
	ProductInfoFormId []ProductInfo   `json:"port_list"`
	Component         []ComponentInfo `json:"rule_tags"`
}
type ProductInfo struct {
	Title        string `json:"title"` // type: string
	Subdomain    string `json:"subdomain"`
	Header       string `json:"header"`
	Port         any    `json:"port"` // type: int
	Protocol     string `json:"protocol"`
	Banner       string `json:"banner"`
	Logo         Icon   `json:"logo"`
	Body         string `json:"body"`
	PortProtocol string `json:"port_protocol"`
}

type ComponentInfo struct {
	Component  string `json:"cn_product"`
	CnCategory string `json:"cn_category"`
	PortArr    []any  `json:"port_arr"`
}

func NewFofaeeAssetsModel(conn ...*elastic.Client) FofaeeAssetsModel {
	return &defaultFofaeeAssetsModel{
		es.GetEsClient(conn...),
		"ips",
		FofaeeAssetsIndex,
	}
}

func (f *FofaeeAssets) IndexName() string {
	return FofaeeAssetsIndex
}

func (f *FofaeeAssets) TypeName() string {
	return FofaeeAssetsType
}

func GenId(userId int, ip string) string {
	return fmt.Sprintf("%d_%s", userId, ip)
}

// ParseId return parsed id result, userId and ip
func ParseId(id string) (userId uint, ip string) {
	split := strings.Split(id, "_")
	if len(split) != 2 {
		return 0, ""
	}

	ip = split[1]
	userId64, _ := strconv.ParseUint(split[0], 10, 0)

	return uint(userId64), ip
}

func Decode(s string) (FofaeeAssets, error) {
	d := FofaeeAssets{}
	err := json.Unmarshal([]byte(s), &d)
	return d, err
}

func (f *FofaeeAssets) GetPortStr() []string {
	var ports []string
	for i := range f.PortList {
		port := cast.ToString(f.PortList[i].Port)
		ports = append(ports, port)
	}
	return ports
}

func (f *FofaeeAssets) GetCnProduct() []string {
	var cnProduct []string
	for i := range f.RuleTags {
		cnProduct = append(cnProduct, f.RuleTags[i].CnProduct)
	}
	return cnProduct
}

func DistinctAssetPortList(l []FofaeeAssetPort) []FofaeeAssetPort {
	ports := make(map[string]struct{})
	var list = make([]FofaeeAssetPort, 0, len(l))
	for i := range l {
		port := cast.ToString(l[i].Port)
		if _, ok := ports[port]; ok {
			continue
		}
		ports[port] = struct{}{}
		list = append(list, l[i])
	}
	return list
}

func (d *defaultFofaeeAssetsModel) FindByIpAndPort(UserId int, list []es.IpPort, status ...int) ([]FofaeeAssets, error) {
	query := elastic.NewBoolQuery()

	should := make([]elastic.Query, 0, len(list))
	for i := range list {
		must := elastic.NewBoolQuery()
		if list[i].Ip != "" && list[i].PortInt > 0 {
			must = must.Must(elastic.NewTermQuery("ip.keyword", list[i].Ip), elastic.NewTermQuery("port_list.port", list[i].PortInt), elastic.NewTermQuery("user_id", UserId))
		} else {
			must = must.Must(elastic.NewTermQuery("ip.keyword", list[i].Ip), elastic.NewTermQuery("user_id", UserId))
		}
		if len(status) > 0 {
			included := make([]any, 0)
			for _, value := range status {
				included = append(included, value)
			}
			must = must.Must(elastic.NewTermsQuery("status", included...))
		}

		should = append(should, must)
	}

	if len(should) > 0 {
		query = query.Should(should...)
	} else {
		query.Must(elastic.NewTermQuery("user_id", UserId))
		if len(status) > 0 {
			included := make([]any, 0)
			for _, value := range status {
				included = append(included, value)
			}
			query.Must(elastic.NewTermsQuery("status", included...))
		}
	}

	s, _ := query.Source()
	esstr, _ := json.Marshal(s)
	fmt.Println("fofaee_assets query json:", string(esstr))

	do, err := d.Client.Scroll().Index(d.Index).
		Query(query).Scroll("5m").Size(2000).Do(context.Background())
	switch err {
	case nil:
	case io.EOF:
		return nil, nil
	default:
		return nil, err
	}

	listData := marshalHandle(do.Hits.Hits)

	scrollId := do.ScrollId
	for {
		result, _ := d.Client.Scroll("5m").
			ScrollId(scrollId).
			Do(context.Background())
		if len(result.Hits.Hits) <= 0 {
			break
		}

		listData = append(listData, marshalHandle(result.Hits.Hits)...)
	}

	return listData, nil
}

func (d *defaultFofaeeAssetsModel) FindByCondition(ctx context.Context, param *FindCondition, page, size int, fields ...string) ([]*FofaeeAssets, int64, error) {
	if ctx == nil {
		ctx = context.Background()
	}
	for {
		select {
		case <-ctx.Done():
			return nil, 0, ctx.Err()
		default:
			q := elastic.NewBoolQuery()
			buildBoolQueryByCondition(q, param)

			var err error
			var service = d.Client.Search(d.Index).Type(d.Type)
			if len(fields) > 0 {
				service = service.StoredFields(fields...)
			}
			var do *elastic.SearchResult
			isPage := page > 0 && size > 0
			if isPage {
				// 分页查询
				do, err = service.Query(q).From(es.GetFrom(page, size)).Size(size).Do(context.Background())
			} else {
				// 不分页查询,每页2000条
				searchQuery := elastic.NewSearchSource().Query(q).Size(2000).Sort("id", true)
				do, err = service.SearchSource(searchQuery).Do(context.Background())
			}

			if err != nil {
				// 如果查询结果为空,则返回空数组
				if elastic.IsNotFound(err) {
					return nil, 0, nil
				}
				return nil, 0, err
			}
			if len(do.Hits.Hits) == 0 {
				return nil, 0, err
			}

			if isPage {
				total := do.Hits.TotalHits
				list := searchHitParseFunc[*FofaeeAssets](do.Hits.Hits)
				return list, total, nil
			}

			// 预估slice长度，避免slice频繁扩容
			estimatedTotal := min(do.Hits.TotalHits, 10000)
			list := make([]*FofaeeAssets, 0, estimatedTotal)
			list = append(list, searchHitParseFunc[*FofaeeAssets](do.Hits.Hits)...)
			lastValue := do.Hits.Hits[len(do.Hits.Hits)-1].Sort
			for {
				searchAfter := elastic.NewSearchSource().Query(q).Size(2000).SearchAfter(lastValue...).Sort("id", true)
				do, err = d.Client.Search().Index(d.Index).Type(d.Type).SearchSource(searchAfter).Do(context.Background())
				if err != nil || len(do.Hits.Hits) == 0 {
					break
				}
				list = append(list, searchHitParseFunc[*FofaeeAssets](do.Hits.Hits)...)
				lastValue = do.Hits.Hits[len(do.Hits.Hits)-1].Sort
			}
			return list, int64(len(list)), err
		}
	}
}

func (d *defaultFofaeeAssetsModel) FindById(ctx context.Context, id string) *AssetInfo {
	boolQuery := elastic.NewBoolQuery()
	asset := AssetInfo{}
	if id == "" {
		return nil
	}
	boolQuery.Must(elastic.NewTermsQuery("_id", id))

	s, _ := boolQuery.Source()
	esQuery, _ := json.Marshal(s)
	fmt.Println("fofaee_assets es query:", string(esQuery))

	result, err := d.Client.Search(d.Index).Type(d.Type).Query(boolQuery).From(0).Size(1).Do(ctx)
	if err == io.EOF {
		return nil
	}
	if err != nil {
		return nil
	}
	for _, hit := range result.Hits.Hits {
		if getErr := json.Unmarshal(*hit.Source, &asset); getErr != nil {
			return nil
		}
	}
	return &asset
}

// FindFullById 根据ID查询完整的FofaeeAssets数据
func (d *defaultFofaeeAssetsModel) FindFullById(ctx context.Context, id string) *FofaeeAssets {
	if id == "" {
		return nil
	}

	// 直接使用Get方法查询文档ID，这比Search更高效
	get, err := d.Client.Get().Index(d.Index).Type(d.Type).Id(id).Do(ctx)
	if err != nil {
		// 文档不存在或其他错误
		log.Debugf("FindFullById: 查询文档失败, id=%s, error=%v", id, err)
		return nil
	}

	if !get.Found {
		log.Debugf("FindFullById: 文档不存在, id=%s", id)
		return nil
	}

	var asset FofaeeAssets
	if err := json.Unmarshal(*get.Source, &asset); err != nil {
		log.Errorf("FindFullById: 解析文档失败, id=%s, error=%v", id, err)
		return nil
	}

	log.Debugf("FindFullById: 查询成功, id=%s", id)
	return &asset
}

func (d *defaultFofaeeAssetsModel) CountIpByUserTask(userID int, taskIds []uint) (int, error) {
	must := elastic.NewBoolQuery()
	must = must.Must(elastic.NewMatchPhraseQuery("user_id", userID))
	if len(taskIds) > 0 {
		included := make([]any, 0, len(taskIds))
		for _, value := range taskIds {
			included = append(included, value)
		}
		must.Must(elastic.NewTermsQuery("task_id", included...))
	}

	agg := elastic.NewCardinalityAggregation().Field("ip.keyword")
	ctx := context.Background()

	sr, err := d.Client.Search(d.Index).Query(must).Aggregation("COUNT", agg).Size(0).Do(ctx)
	switch err {
	case nil:
	case io.EOF:
		sr, err = d.Client.Search(d.Index).Query(must).Aggregation("COUNT", agg).Size(0).Do(ctx)
		if err != nil {
			return 0, err
		}
	default:
		return 0, err
	}

	term, ok := sr.Aggregations["COUNT"]
	if !ok {
		return 0, errors.New("统计错误")
	}

	var value = struct {
		V any `json:"value"`
	}{}

	b, _ := term.MarshalJSON()
	json.Unmarshal(b, &value)

	return decodeJson(value.V)
}

func (d *defaultFofaeeAssetsModel) CountRuleByUserTask(userID int, taskIds []uint) (int, error) {
	must := elastic.NewBoolQuery()
	must = must.Must(elastic.NewMatchPhraseQuery("user_id", userID))
	if len(taskIds) > 0 {
		included := make([]any, 0, len(taskIds))
		for _, value := range taskIds {
			included = append(included, value)
		}
		must.Must(elastic.NewTermsQuery("task_id", included...))
	}

	s := elastic.NewScript("if(params._source.rule_tags != null ) {return params._source.rule_tags.size();} else return 0;")
	agg := elastic.NewSumAggregation().Script(s)

	sr, err := d.Client.
		Search(d.Index).
		Query(must).
		Aggregation("productNum", agg).
		Size(0).
		Do(context.Background())
	switch err {
	case nil:
	case io.EOF:
		sr, err = d.Client.Search(d.Index).Query(must).
			Aggregation("productNum", agg).Size(0).Do(context.Background())
		if err != nil {
			return 0, err
		}
	default:
		return 0, err
	}

	term, ok := sr.Aggregations["productNum"]
	if !ok {
		return 0, errors.New("统计错误")
	}

	var value = struct {
		V any `json:"value"`
	}{}

	b, _ := term.MarshalJSON()
	json.Unmarshal(b, &value)

	return decodeJson(value.V)
}

func decodeJson(value any) (int, error) {
	return cast.ToInt(value), nil
}

func (d *defaultFofaeeAssetsModel) CountByStatus(userID int, status []int) (int, error) {
	must := elastic.NewBoolQuery()
	must = must.Must(elastic.NewTermQuery("user_id", userID),
		elastic.NewMatchPhraseQuery("user_id", userID))

	s := elastic.NewScript("if(params._source.rule_tags != null ) {return params._source.rule_tags.size();} else return 0;")
	agg := elastic.NewSumAggregation().Script(s)

	sr, err := d.Client.Search(d.Index).Query(must).Aggregation("productNum", agg).Size(0).Do(context.Background())
	switch err {
	case nil:
	case io.EOF:
		sr, err = d.Client.Search(d.Index).Query(must).Aggregation("productNum", agg).Size(0).Do(context.Background())
		if err != nil {
			return 0, err
		}
	default:
		return 0, err
	}

	term, ok := sr.Aggregations["productNum"]
	if !ok {
		return 0, errors.New("统计错误")
	}

	var value = struct {
		V any `json:"value"`
	}{}

	b, _ := term.MarshalJSON()
	json.Unmarshal(b, &value)

	return decodeJson(value.V)
}

type FofaeeAssetsGroupByStatus struct {
	Status int
	Total  int64
}

func (d *defaultFofaeeAssetsModel) GroupByStatusWithDetectTask(userId uint, detectAssetTasks []uint) ([]FofaeeAssetsGroupByStatus, error) {
	query := elastic.NewBoolQuery()
	if userId > 0 {
		query.Must(elastic.NewTermQuery("user_id", userId))
	}
	if len(detectAssetTasks) > 0 {
		included := make([]any, 0)
		for _, value := range detectAssetTasks {
			included = append(included, value)
		}
		query.Must(elastic.NewTermsQuery("detect_assets_tasks_id", included...))
	}

	aggQuery := d.Client.Search().Index(d.Index).Query(query).
		Aggregation("status", elastic.NewTermsAggregation().Field("status"))

	do, err := aggQuery.Do(context.Background())
	if err != nil || len(do.Aggregations) == 0 {
		return nil, err
	}

	statusRaw, ok := do.Aggregations["status"]
	if !ok {
		return nil, nil
	}
	singleAgg := new(elastic.AggregationBucketKeyItems)
	if err = json.Unmarshal(*statusRaw, singleAgg); err != nil {
		return nil, err
	}

	var list = make([]FofaeeAssetsGroupByStatus, 0, len(singleAgg.Buckets))
	m := make(map[int]int64)
	for i := range singleAgg.Buckets {
		status := cast.ToInt(singleAgg.Buckets[i].Key)
		m[status] += singleAgg.Buckets[i].DocCount
	}
	for k, v := range m {
		list = append(list, FofaeeAssetsGroupByStatus{Status: k, Total: v})
	}

	return list, nil
}

func (d *defaultFofaeeAssetsModel) AggByRule(userID uint64, size int) ([]CountData, error) {
	q := elastic.NewBoolQuery()
	q.Must(elastic.NewTermsQuery("status", []any{StatusConfirmAsset, StatusUploadAsset}...)) // 台账资产
	q.Must(elastic.NewTermQuery("user_id", userID))

	aggs := elastic.NewTermsAggregation().Field("rule_tags.cn_product.raw").Size(size)

	do, err := d.Client.Search().Index(d.Index).Query(q).
		Aggregation("group_by_rule", aggs).Size(0).Do(context.Background())
	switch {
	case errors.Is(err, io.EOF):
		return nil, errors.New("未查询到数据")
	case err != nil:
		return nil, err
	}

	term, ok := do.Aggregations["group_by_rule"]
	if !ok {
		return nil, errors.New("未查询到数据")
	}

	b, err := term.MarshalJSON()
	if err != nil {
		return nil, err
	}

	var agg countDataBuckets
	if err = json.Unmarshal(b, &agg); err != nil {
		return nil, err
	}
	return agg.Buckets, nil
}

func (d *defaultFofaeeAssetsModel) CountByStatusAndUserIDAndRange(userID int, status []any, start, end string) (int64, error) {
	q := elastic.NewBoolQuery()
	q = q.Must(elastic.NewTermsQuery("status", status...), elastic.NewTermQuery("user_id", userID), elastic.NewRangeQuery("created_at").Gte(start).Lte(end))

	do, err := d.Client.Count().Index(d.Index).Query(q).Do(context.Background())
	if err != nil {
		if errors.Is(err, io.EOF) {
			return 0, errors.New("未查询到数据")
		}
		return 0, err
	}

	return do, nil
}

func marshalHandle(hits []*elastic.SearchHit) []FofaeeAssets {
	var list = make([]FofaeeAssets, 0, len(hits))
	for _, item := range hits {
		b, err := item.Source.MarshalJSON()
		if err != nil {
			continue
		}

		var record FofaeeAssets
		if err = json.Unmarshal(b, &record); err != nil {
			log.Infof("ES index name: fofaee_assets, json.Unmarshal: %v\n", err)
			continue
		}
		list = append(list, record)
	}
	return list
}

func (d *defaultFofaeeAssetsModel) Updates(ctx context.Context, list ...*FofaeeAssets) error {
	req := d.Client.Bulk()
	for i := range list {
		id := utils.If(list[i].Id == "", GenId(list[i].UserId, list[i].Ip), list[i].Id)
		if list[i].UpdatedAt == "" {
			list[i].UpdatedAt = utils.CurrentTime()
		}

		doc := elastic.NewBulkUpdateRequest().Index(d.Index).Type(d.Type).Id(id).Doc(list[i])
		req.Add(doc)
	}

	if req.NumberOfActions() <= 0 {
		return nil
	}
	do, err := req.Do(ctx)
	if err != nil {
		return err
	}

	if len(do.Failed()) > 0 {
		for _, v := range do.Failed() {
			log.Warnf("es index_name: %s, doc id: %s update failed: %s\n", v.Index, v.Id, v.Error.Reason)
		}
	}
	return nil
}

func (d *defaultFofaeeAssetsModel) UpdatePorts(ctx context.Context, m map[string]UpdatePortsHost) error {
	bulkReq := d.Client.Bulk()
	for k := range m {
		um := make(map[string]any)
		if len(m[k].List) > 0 {
			um["port_list"] = m[k].List
		}
		if len(m[k].Hosts) > 0 {
			um["hosts"] = utils.ListDistinct(m[k].Hosts)
		}
		if len(m[k].HostList) > 0 {
			um["host_list"] = m[k].HostList
		}
		if len(m[k].ClueCompanyName) > 0 {
			um["clue_company_name"] = m[k].ClueCompanyName
		}
		if len(m[k].RuleTags) > 0 {
			um["rule_tags"] = m[k].RuleTags
		}
		if len(m[k].Tags) > 0 {
			um["tags"] = m[k].Tags
		}
		um["online_state"] = m[k].OnlineState
		um["status"] = m[k].Status
		um["updated_at"] = utils.CurrentTime()

		doc := elastic.NewBulkUpdateRequest().Type(d.Type).Index(d.Index).Id(k).Doc(um).DocAsUpsert(true)
		bulkReq.Add(doc)
	}

	if bulkReq.NumberOfActions() <= 0 {
		return nil
	}

	rsp, err := bulkReq.Do(ctx)
	if err != nil {
		return err
	}

	if len(rsp.Failed()) > 0 {
		s, _ := jsoniter.MarshalToString(rsp.Failed())
		return fmt.Errorf("some doc upsert failed, err: %s", s)
	}
	return nil
}

func (d *defaultFofaeeAssetsModel) Create(ctx context.Context, l []*FofaeeAssets) (successIds, failedIds []string, err error) {
	req := d.Client.Bulk().Index(d.Index)
	for i := range l {
		if l[i].Id == "" {
			l[i].Id = GenId(l[i].UserId, l[i].Ip)
		}
		l[i].CreatedAt = utils.CurrentTime()
		l[i].UpdatedAt = utils.CurrentTime()
		doc := elastic.NewBulkIndexRequest().Type(d.Type).Id(l[i].Id).Doc(l[i])
		req.Add(doc)
	}

	if req.NumberOfActions() <= 0 {
		return nil, nil, nil
	}

	resp, err := req.Refresh("true").Do(ctx)
	if err != nil {
		return nil, nil, err
	}

	for _, failed := range resp.Failed() {
		failedIds = append(failedIds, failed.Id)
		s, _ := json.Marshal(failed.Error)
		log.Errorf("es index: %s insert failed, doc id: %s, err: %s\n", d.Index, failed.Id, failed, string(s))
	}
	for _, v := range resp.Succeeded() {
		successIds = append(successIds, v.Id)
	}

	return successIds, failedIds, nil
}

func (d *defaultFofaeeAssetsModel) Count(ctx context.Context, query *elastic.BoolQuery) (int64, error) {
	result, err := d.Client.Count(d.Index).Type(d.Type).Query(query).Do(ctx)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func (d *defaultFofaeeAssetsModel) DeleteByTaskId(ctx context.Context, taskId uint64) error {
	// 构建 script 查询
	script := elastic.NewScript(`doc['task_id'].length == 1 && doc['task_id'][0] == params.value`).
		Lang("painless").
		Param("value", taskId)

	// 构建 query
	query := elastic.NewScriptQuery(script)

	_, err := d.Client.DeleteByQuery(d.Index).Type(d.Type).Query(query).Do(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (d *defaultFofaeeAssetsModel) FindUserIdsAndIpByTaskId(ctx context.Context, taskId uint64) ([]*FofaeeAssets, error) {
	// 构建 script 查询
	script := elastic.NewScript(`doc['task_id'].length == 1 && doc['task_id'][0] == params.value`).
		Lang("painless").
		Param("value", taskId)
	query := elastic.NewScriptQuery(script)

	scroll := d.Client.Scroll(d.Index).Size(1000).Type(d.Type).Query(query)
	scroll.Scroll("1m")
	// 设置存储字段
	scroll.FetchSourceContext(elastic.NewFetchSourceContext(true).Include("_id", "user_id", "ip"))

	doResult, err := scroll.Do(ctx)
	if err != nil {
		return nil, err
	}

	var list = make([]*FofaeeAssets, 0, len(doResult.Hits.Hits))
	for _, hit := range doResult.Hits.Hits {
		var record FofaeeAssets
		if err = json.Unmarshal(*hit.Source, &record); err != nil {
			log.Infof("ES index name: fofaee_assets, json.Unmarshal: %v\n", err)
			continue
		}
		list = append(list, &record)
	}

	return list, nil
}

// UnmarshalJSON 自定义JSON解析方法，处理clue_company_name字段的类型转换
func (ar *AssetReason) UnmarshalJSON(data []byte) error {
	// 定义一个临时结构体，clue_company_name使用any类型
	type TempAssetReason struct {
		Id              int    `json:"id"`
		Type            int    `json:"type"`
		Content         string `json:"content"`
		GroupId         int    `json:"group_id"`
		ClueCompanyName any    `json:"clue_company_name"`
		Source          int    `json:"source"`
	}

	var temp TempAssetReason
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	// 复制基本字段
	ar.Id = temp.Id
	ar.Type = temp.Type
	ar.Content = temp.Content
	ar.GroupId = temp.GroupId
	ar.Source = temp.Source

	// 处理clue_company_name字段的类型转换
	switch v := temp.ClueCompanyName.(type) {
	case string:
		ar.ClueCompanyName = v
	case []interface{}:
		if len(v) > 0 {
			if str, ok := v[0].(string); ok {
				ar.ClueCompanyName = str
			} else {
				ar.ClueCompanyName = cast.ToString(v[0])
			}
		} else {
			ar.ClueCompanyName = ""
		}
	case []string:
		if len(v) > 0 {
			ar.ClueCompanyName = v[0]
		} else {
			ar.ClueCompanyName = ""
		}
	case nil:
		ar.ClueCompanyName = ""
	default:
		ar.ClueCompanyName = cast.ToString(v)
	}

	return nil
}
