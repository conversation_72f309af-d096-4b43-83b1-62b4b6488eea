package recommend_record

import (
	"testing"
	"time"

	"micro-service/initialize/es"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"

	"github.com/stretchr/testify/assert"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = es.GetInstance(cfg.LoadElastic())
}

func TestFindById(t *testing.T) {
	initCfg()

	id := "233c9e5530be68f252ca6fc1db77f538"
	r, err := NewRecommendRecordModel().FindByID(id)
	assert.Nil(t, err)
	assert.Equal(t, id, r.Id)
}

func TestCreate(t *testing.T) {
	initCfg()

	id := "123456789ca6fc1db77f538"
	r := RecommendRecord{
		Id:        id,
		Flag:      id,
		TaskName:  "钓鱼仿冒测绘任务",
		UserId:    100,
		Status:    StatusFinished,
		CreatedAt: time.Now().Format(utils.DateTimeLayout),
		UpdatedAt: time.Now().Format(utils.DateTimeLayout),
	}
	err := NewRecommendRecordModel().Create(r)
	assert.Nil(t, err)
}

func TestUpdate(t *testing.T) {
	initCfg()

	id := "123456789ca6fc1db77f538"
	r := RecommendRecord{
		Id:        id,
		Flag:      id,
		CompanyId: 100,
		UserId:    500,
		Status:    0,
	}
	err := NewRecommendRecordModel().Update(r)
	assert.Nil(t, err)
}

func TestUpdateAny(t *testing.T) {
	initCfg()

	id := "123456789ca6fc1db77f538"
	m := map[string]any{
		"user_id":   100,
		"status":    1,
		"task_name": "钓鱼仿冒测绘任务",
	}
	err := NewRecommendRecordModel().UpdateAny(id, m)
	assert.Nil(t, err)
}

func TestDeleteById(t *testing.T) {
	initCfg()

	id := "123456789ca6fc1db77f538"
	err := NewRecommendRecordModel().DeleteById(id)
	assert.Nil(t, err)
}

func TestDefaultRecommendRecordModel_CountByUserID(t *testing.T) {
	initCfg()

	r, e := NewRecommendRecordModel(es.GetInstance()).CountByUserID("17", StatusFinished)
	if e != nil {
		t.Error(e)
		return
	}

	t.Log(r)
}
