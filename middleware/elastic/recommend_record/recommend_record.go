package recommend_record

import (
	"context"
	"encoding/json"
	"time"

	"github.com/olivere/elastic"

	es "micro-service/middleware/elastic"
	"micro-service/pkg/utils"
)

const (
	// 资产推荐任务记录索引名
	indexName = "foradar_recommend_record"
	docType   = "record"
)

const (
	StatusDefault  = iota // 待处理 | 等待中
	StatusDoing           // 进行中
	StatusFinished        // 完成
	StatusFailed          // 失败
)

type (
	RecommendRecordModel interface {
		FindByID(id string) (*RecommendRecord, error)
		Create(RecommendRecord) error
		// Update will update all fields value of RecommendRecord
		Update(RecommendRecord) error
		UpdateAny(id string, m map[string]any) error
		DeleteById(id string) error
		CountByUserID(userId string, status int) (int64, error)
	}

	defaultRecommendRecordModel struct{ *elastic.Client }

	RecommendRecord struct {
		Id       string `json:"id"`
		Flag     string `json:"flag"`
		TaskName string `json:"task_name"`
		Cert     []struct {
			GroupId         int    `json:"group_id"`
			Id              int    `json:"id"`
			Source          int    `json:"source"`
			Type            int    `json:"type"`
			Content         string `json:"content"`
			ClueCompanyName string `json:"clue_company_name"`
		} `json:"cert"`
		Title []struct {
			GroupId         int    `json:"group_id"`
			Id              int    `json:"id"`
			Source          int    `json:"source"`
			Type            int    `json:"type"`
			Content         string `json:"content"`
			ClueCompanyName string `json:"clue_company_name"`
		} `json:"title"`
		StartAt             string  `json:"start_at"`
		CronId              any     `json:"cron_id"`
		ClueId              []int   `json:"clue_id"`
		Icp                 any     `json:"icp"`
		Logo                any     `json:"logo"`
		CompanyId           int     `json:"company_id"`
		GroupName           string  `json:"group_name"`
		Count               int     `json:"count"`
		OpId                int     `json:"op_id"`
		Confirm             int     `json:"confirm"`
		Node                string  `json:"node"`
		DetectAssetsTasksId int     `json:"detect_assets_tasks_id"`
		GroupId             int     `json:"group_id"`
		UserId              any     `json:"user_id"` // int
		Domain              any     `json:"domain"`
		Subdomain           any     `json:"subdomain"`
		Progress            float64 `json:"progress"`
		Status              any     `json:"status"`     // type: int
		CreatedAt           string  `json:"created_at"` // time.Time to string
		UpdatedAt           string  `json:"updated_at"` // time.Time to string
	}
)

func (m *RecommendRecord) IndexName() string {
	return indexName
}
func NewRecommendRecordModel(conn ...*elastic.Client) RecommendRecordModel {
	return &defaultRecommendRecordModel{
		Client: es.GetEsClient(conn...),
	}
}

func (d *defaultRecommendRecordModel) FindByID(id string) (*RecommendRecord, error) {
	var item RecommendRecord
	result, err := d.Client.Get().Index(indexName).Type(docType).Id(id).Do(context.Background())
	if err != nil {
		return nil, err
	}

	if result.Found {
		err = json.Unmarshal(*result.Source, &item)
	}
	return &item, err
}

func (d *defaultRecommendRecordModel) Update(record RecommendRecord) error {
	if record.CreatedAt == "" {
		record.CreatedAt = time.Now().Format(utils.DateTimeLayout)
	}
	record.UpdatedAt = time.Now().Format(utils.DateTimeLayout)

	_, err := d.Client.Update().Index(indexName).Type(docType).
		Id(record.Id).Doc(record).Do(context.Background())

	return err
}

func (d *defaultRecommendRecordModel) UpdateAny(id string, m map[string]any) error {
	m["updated_at"] = time.Now().Format(utils.DateTimeLayout)

	_, err := d.Client.Update().Index(indexName).Type(docType).
		Id(id).Doc(m).Do(context.Background())

	return err
}

func (d *defaultRecommendRecordModel) Create(record RecommendRecord) error {
	record.CreatedAt = time.Now().Format(utils.DateTimeLayout)
	record.UpdatedAt = time.Now().Format(utils.DateTimeLayout)

	_, err := d.Client.Index().Index(indexName).Type(docType).
		Id(record.Id).BodyJson(record).Do(context.Background())
	if err != nil {
		return err
	}
	return nil
}

func (d *defaultRecommendRecordModel) DeleteById(id string) error {
	_, err := d.Client.Delete().Index(indexName).Type(docType).Id(id).Do(context.Background())
	return err
}

func (d *defaultRecommendRecordModel) CountByUserID(userId string, status int) (int64, error) {
	query := elastic.NewBoolQuery()
	query.Must(elastic.NewTermQuery("user_id", userId))
	if status != -1 {
		query.Must(elastic.NewTermQuery("status", status))
	}

	cnt, err := d.Client.Count(indexName).Type(docType).Query(query).Do(context.Background())
	return cnt, err
}
